{"name": "parcel-react-client-starter", "private": true, "version": "0.0.0", "source": "src/index.html", "scripts": {"start": "parcel", "build": "parcel build"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "antd": "^3.26.20", "insert-css": "^2.0.0", "jsplumb": "^2.15.6", "react": "^17.0.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^9.5.1", "react-dom": "^17.0.2"}, "devDependencies": {"@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "parcel": "^2.14.0", "process": "^0.11.10"}}