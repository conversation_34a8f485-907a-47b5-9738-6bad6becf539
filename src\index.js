import React from 'react';
import ReactDOM from 'react-dom';
import App from './App.js';
import App1 from './App1.js';
import App2 from './App2.js';
import HTML5Backend from "react-dnd-html5-backend";
import {DndProvider} from "react-dnd";
import './common.css';

ReactDOM.render(
    <DndProvider backend={HTML5Backend}>
        <App/>
    </DndProvider>,
    document.getElementById("app")
);
// ReactDOM.render(<App1/>, document.getElementById("app"));
// ReactDOM.render(<App2/>, document.getElementById("app"));
