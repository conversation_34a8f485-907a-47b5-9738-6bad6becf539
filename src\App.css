.process-building {
    background-color: #eee;
    grid-template-columns: 240px 1fr;
    place-items: center;
    gap: 20px;
    min-height: 400px;
    padding: 20px 0;
    display: grid;
    height: calc(100vh - 40px);
}

.dir {
    height: 100%;
    padding: 12px;
    border: 1px solid black;
    user-select: none;
    font-size: 16px;
}

.dir * {
    transition: 0.4s ease-out;
}

.dir > div {
    cursor: pointer;
    height: 32px;
}

.dir ul {
    margin: 0;
    transition: max-height 0.3s ease-out;
    overflow: hidden;
    cursor: pointer;
}

.dir .title {
    display: grid;
    grid-template-columns: 20px 1fr;
    align-items: center;
}

.dir .arrow-container {
    display: grid;
    place-content: center;
}

.content {
    padding: 12px 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-auto-rows: max-content;
    gap: 20px;
}

.content > div {
    padding: 8px;
    border: 1px solid #00d7d5;
}

.container {
    height: 100%;
    width: 100%;
}

#flow-main {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.window {
    min-width: 280px;
    min-height: 80px;
    position: absolute;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    cursor: move;
    user-select: none;
}

.drop {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
}

.main-title {
    position: absolute;
    top: 20px;
    left: 0;
    width: 100%;
    text-align: center;
}

.node {
    padding: 8px 12px;
    position: absolute;
    width: max-content;
    display: grid;
    place-items: center;
    grid-template-columns: 1fr 32px 32px;
    background: linear-gradient(#00BCBB, #009190);
}

.node i {
    display: grid;
    place-items: center;
}

fieldset.frame {
    position: absolute;
    min-width: 200px;
    min-height: 40px;
}

fieldset.frame legend input {
    max-width: 32px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input:focus-visible {
    outline: none;
}

.resizing {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 24px;
    height: 24px;
    cursor: nwse-resize;
    background: transparent;
    border-radius: 50%;
    display: grid;
    place-items: center;
    /*z-index: 10;*/
    user-select: none;
    rotate: 90deg;
}