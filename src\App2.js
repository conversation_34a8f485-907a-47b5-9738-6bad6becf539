import {useEffect, useRef} from 'react';
import {Graph} from '@antv/x6';

export default function App2() {
    const ui_container = useRef(null);
    useEffect(() => {
        const graph = new Graph({
            container: ui_container.current, grid: true, embedding: {enabled: true,},
            highlighting: {embedding: {name: 'stroke', args: {padding: -1, attrs: {stroke: '#73d13d',},},},},
        })

        const source = graph.addNode({
            x: 80, y: 100, width: 80, height: 40, label: 'Child', zIndex: 10,
            attrs: {
                body: {stroke: 'none', fill: '#3199FF',},
                label: {fill: '#fff', fontSize: 12,},
            },
        })

        const window_1 = graph.addNode({

            shape: 'rect', x: 40, y: 40, width: 360, height: 160, zIndex: 1, label: 'Parent',
            attrs: {
                body: {fill: '#fffbe6', stroke: '#ffffff',},
                label: {fontSize: 12,},
            },
        })

        const window_2 = graph.addNode({
            x: 420, y: 40, width: 360, height: 160, zIndex: 1, label: 'Parent',
            attrs: {
                body: {fill: '#fffbe6', stroke: '#000000',},
                label: {fontSize: 12,},
            },
        })

        window_1.addChild(source)

        let ctrlPressed = false
        const embedPadding = 20

        graph.on('node:embedding', ({e}) => {
            ctrlPressed = e.metaKey || e.ctrlKey
        })

        graph.on('node:embedded', () => {
            ctrlPressed = false
        })

        graph.on('node:change:size', ({node, options}) => {
            if (options.skipParentHandler) return

            const children = node.getChildren()
            if (children && children.length) {
                node.prop('originSize', node.getSize())
            }
        })

        graph.on('node:change:position', ({node, options}) => {
            if (options.skipParentHandler || ctrlPressed) {
                return
            }

            const children = node.getChildren()
            if (children && children.length) {
                node.prop('originPosition', node.getPosition())
            }

            const parent = node.getParent()
            if (parent && parent.isNode()) {
                let originSize = parent.prop('originSize')
                if (originSize == null) {
                    originSize = parent.getSize()
                    parent.prop('originSize', originSize)
                }

                let originPosition = parent.prop('originPosition')
                if (originPosition == null) {
                    originPosition = parent.getPosition()
                    parent.prop('originPosition', originPosition)
                }

                let x = originPosition.x
                let y = originPosition.y
                let cornerX = originPosition.x + originSize.width
                let cornerY = originPosition.y + originSize.height
                let hasChange = false

                const children = parent.getChildren()
                if (children) {
                    children.forEach((child) => {
                        const bbox = child.getBBox().inflate(embedPadding)
                        const corner = bbox.getCorner()

                        if (bbox.x < x) {
                            x = bbox.x
                            hasChange = true
                        }

                        if (bbox.y < y) {
                            y = bbox.y
                            hasChange = true
                        }

                        if (corner.x > cornerX) {
                            cornerX = corner.x
                            hasChange = true
                        }

                        if (corner.y > cornerY) {
                            cornerY = corner.y
                            hasChange = true
                        }
                    })
                }

                if (hasChange) {
                    parent.prop(
                        {
                            position: {x, y},
                            size: {width: cornerX - x, height: cornerY - y},
                        },
                        {skipParentHandler: true},
                    )
                }
            }
        });
    });
    return (
        <div ref={ui_container} id={'container'}>

        </div>
    )
}