{"name": "parcel-react-client-starter", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "parcel-react-client-starter", "version": "0.0.0", "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "antd": "^3.26.20", "insert-css": "^2.0.0", "jsplumb": "^2.15.6", "react": "^17.0.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^9.5.1", "react-dom": "^17.0.2"}, "devDependencies": {"@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "parcel": "^2.14.0", "process": "^0.11.10"}}, "node_modules/@ant-design/colors": {"version": "3.2.2", "resolved": "https://registry.npmmirror.com/@ant-design/colors/-/colors-3.2.2.tgz", "integrity": "sha512-YKgNbG2dlzqMhA9NtI3/pbY16m3Yl/EeWBRa+lB1X1YaYxHrxNexiQYCLTWO/uDvAjLFMEDU+zR901waBtMtjQ==", "license": "MIT", "dependencies": {"tinycolor2": "^1.4.1"}}, "node_modules/@ant-design/create-react-context": {"version": "0.2.6", "resolved": "https://registry.npmmirror.com/@ant-design/create-react-context/-/create-react-context-0.2.6.tgz", "integrity": "sha512-pHUuaE50/WEek4w2Q+QYVieLPIGfXM+nUsGSsg8xO6oHBw7dfd14Ws/6q3/L6eZ60zjUiv3WUlSzpWyCOXLqbQ==", "license": "MIT", "dependencies": {"gud": "^1.0.0", "warning": "^4.0.3"}, "peerDependencies": {"prop-types": ">=15.0.0", "react": "^0.14.0 || >=15.0.0"}}, "node_modules/@ant-design/css-animation": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/@ant-design/css-animation/-/css-animation-1.7.3.tgz", "integrity": "sha512-LrX0OGZtW+W6iLnTAqnTaoIsRelYeuLZWsrmBJFUXDALQphPsN8cE5DCsmoSlL0QYb94BQxINiuS70Ar/8BNgA==", "license": "MIT"}, "node_modules/@ant-design/icons": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/@ant-design/icons/-/icons-2.1.1.tgz", "integrity": "sha512-jCH+k2Vjlno4YWl6g535nHR09PwCEmTBKAG6VqF+rhkrSPRLfgpU2maagwbZPLjaHuU5Jd1DFQ2KJpQuI6uG8w==", "license": "MIT"}, "node_modules/@antv/x6": {"version": "2.18.1", "resolved": "https://registry.npmmirror.com/@antv/x6/-/x6-2.18.1.tgz", "integrity": "sha512-FkWdbLOpN9J7dfJ+kiBxzowSx2N6syBily13NMVdMs+wqC6Eo5sLXWCZjQHateTFWgFw7ZGi2y9o3Pmdov1sXw==", "license": "MIT", "dependencies": {"@antv/x6-common": "^2.0.16", "@antv/x6-geometry": "^2.0.5", "utility-types": "^3.10.0"}}, "node_modules/@antv/x6-common": {"version": "2.0.17", "resolved": "https://registry.npmmirror.com/@antv/x6-common/-/x6-common-2.0.17.tgz", "integrity": "sha512-37g7vmRkNdYzZPdwjaMSZEGv/MMH0S4r70/Jwoab1mioycmuIBN73iyziX8m56BvJSDucZ3J/6DU07otWqzS6A==", "license": "MIT", "dependencies": {"lodash-es": "^4.17.15", "utility-types": "^3.10.0"}}, "node_modules/@antv/x6-geometry": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/@antv/x6-geometry/-/x6-geometry-2.0.5.tgz", "integrity": "sha512-MId6riEQkxphBpVeTcL4ZNXL4lScyvDEPLyIafvWMcWNTGK0jgkK7N20XSzqt8ltJb0mGUso5s56mrk8ysHu2A==", "license": "MIT"}, "node_modules/@antv/x6-plugin-clipboard": {"version": "2.1.6", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-clipboard/-/x6-plugin-clipboard-2.1.6.tgz", "integrity": "sha512-roZPLnZx6PK8MBvee0QMo90fz/TXeF0WNe4EGin2NBq5M1I5XTWrYvA6N2XVIiWAAI67gjQeEE8TpkL7f8QdqA==", "license": "MIT", "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-dnd": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-dnd/-/x6-plugin-dnd-2.1.1.tgz", "integrity": "sha512-v0szzik1RkadPDn4Qi5mOSaB2AeI78D40/YuCYbPVzplG+HydGsHwO3MLTgJPQ+R5n0eM0W5F850p1VfTOHR7g==", "license": "MIT", "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-history": {"version": "2.2.4", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-history/-/x6-plugin-history-2.2.4.tgz", "integrity": "sha512-9gHHvEW4Fla+1hxUV49zNgJyIMoV9CjVM52MrFgAJcvyRn1Kvxz4MfxiKlG+DEZUs+/zvfjl9pS6gJOd8laRkg==", "license": "MIT", "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-keyboard": {"version": "2.2.3", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-keyboard/-/x6-plugin-keyboard-2.2.3.tgz", "integrity": "sha512-pnCIC+mDyKKfkcDyLePfGxKVIqXBcldTgannITkHC1kc0IafRS1GMvzpvuDGrM5haRYd6Nwz8kjkJyHkJE4GPA==", "license": "MIT", "dependencies": {"mousetrap": "^1.6.5"}, "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-selection": {"version": "2.2.2", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-selection/-/x6-plugin-selection-2.2.2.tgz", "integrity": "sha512-s2gtR9Onlhr7HOHqyqg0d+4sG76JCcQEbvrZZ64XmSChlvieIPlC3YtH4dg1KMNhYIuBmBmpSum6S0eVTEiPQw==", "license": "MIT", "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-snapline": {"version": "2.1.7", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-snapline/-/x6-plugin-snapline-2.1.7.tgz", "integrity": "sha512-AsysoCb9vES0U2USNhEpYuO/W8I0aYfkhlbee5Kt4NYiMfQfZKQyqW/YjDVaS2pm38C1NKu1LdPVk/BBr4CasA==", "license": "MIT", "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-stencil": {"version": "2.1.5", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-stencil/-/x6-plugin-stencil-2.1.5.tgz", "integrity": "sha512-q7wx7XRMFkUKPv3WsHkvZda6O1GW+6q6H/+c1lcrwlQoEKOFv1Djc4Hu2J4SGhV2z98P2JLfVJiT5m7YoOoCHw==", "license": "MIT", "dependencies": {"@antv/x6-plugin-dnd": "^2.x"}, "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@antv/x6-plugin-transform": {"version": "2.1.8", "resolved": "https://registry.npmmirror.com/@antv/x6-plugin-transform/-/x6-plugin-transform-2.1.8.tgz", "integrity": "sha512-GvJuiJ4BKp0H7+qx3R1I+Vzbw5gXp9+oByXo/WyVxE3urOC7LC5sqnaDfIjyYMN6ROLPYPZraLSeSyYBgMgcDw==", "license": "MIT", "peerDependencies": {"@antv/x6": "^2.x"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@lezer/common": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/@lezer/common/-/common-1.2.3.tgz", "integrity": "sha512-w7ojc8ejBqr2REPsWxJjrMFsA/ysDCFICn8zEOR9mrqzOu2amhITYuLD8ag6XZf0CFXDrhKqw7+tW8cX66NaDA==", "dev": true, "license": "MIT"}, "node_modules/@lezer/lr": {"version": "1.4.2", "resolved": "https://registry.npmmirror.com/@lezer/lr/-/lr-1.4.2.tgz", "integrity": "sha512-pu0K1jCIdnQ12aWNaAVU5bzi7Bd1w54J3ECgANPmYLtQKP0HBj2cE/5coBD66MT10xbtIuUr7tg0Shbsvk0mDA==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.0.0"}}, "node_modules/@lmdb/lmdb-darwin-arm64": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/@lmdb/lmdb-darwin-arm64/-/lmdb-darwin-arm64-2.8.5.tgz", "integrity": "sha512-KPDeVScZgA1oq0CiPBcOa3kHIqU+pTOwRFDIhxvmf8CTNvqdZQYp5cCKW0bUk69VygB2PuTiINFWbY78aR2pQw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@lmdb/lmdb-darwin-x64": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/@lmdb/lmdb-darwin-x64/-/lmdb-darwin-x64-2.8.5.tgz", "integrity": "sha512-w/sLhN4T7MW1nB3R/U8WK5BgQLz904wh+/SmA2jD8NnF7BLLoUgflCNxOeSPOWp8geP6nP/+VjWzZVip7rZ1ug==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@lmdb/lmdb-linux-arm": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/@lmdb/lmdb-linux-arm/-/lmdb-linux-arm-2.8.5.tgz", "integrity": "sha512-c0TGMbm2M55pwTDIfkDLB6BpIsgxV4PjYck2HiOX+cy/JWiBXz32lYbarPqejKs9Flm7YVAKSILUducU9g2RVg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@lmdb/lmdb-linux-arm64": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/@lmdb/lmdb-linux-arm64/-/lmdb-linux-arm64-2.8.5.tgz", "integrity": "sha512-vtbZRHH5UDlL01TT5jB576Zox3+hdyogvpcbvVJlmU5PdL3c5V7cj1EODdh1CHPksRl+cws/58ugEHi8bcj4Ww==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@lmdb/lmdb-linux-x64": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/@lmdb/lmdb-linux-x64/-/lmdb-linux-x64-2.8.5.tgz", "integrity": "sha512-Xkc8IUx9aEhP0zvgeKy7IQ3ReX2N8N1L0WPcQwnZweWmOuKfwpS3GRIYqLtK5za/w3E60zhFfNdS+3pBZPytqQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@lmdb/lmdb-win32-x64": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/@lmdb/lmdb-win32-x64/-/lmdb-win32-x64-2.8.5.tgz", "integrity": "sha512-4wvrf5BgnR8RpogHhtpCPJMKBmvyZPhhUtEwMJbXh0ni2BucpfF07jlmyM11zRqQ2XIq6PbC2j7W7UCCcm1rRQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@mischnic/json-sourcemap": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/@mischnic/json-sourcemap/-/json-sourcemap-0.1.1.tgz", "integrity": "sha512-iA7+tyVqfrATAIsIRWQG+a7ZLLD0VaOCKV2Wd/v4mqIU3J9c4jx9p7S0nw1XH3gJCKNBOOwACOPYYSUu9pgT+w==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.0.0", "@lezer/lr": "^1.0.0", "json5": "^2.2.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@msgpackr-extract/msgpackr-extract-darwin-arm64": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/@msgpackr-extract/msgpackr-extract-darwin-arm64/-/msgpackr-extract-darwin-arm64-3.0.3.tgz", "integrity": "sha512-QZHtlVgbAdy2zAqNA9Gu1UpIuI8Xvsd1v8ic6B2pZmeFnFcMWiPLfWXh7TVw4eGEZ/C9TH281KwhVoeQUKbyjw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@msgpackr-extract/msgpackr-extract-darwin-x64": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/@msgpackr-extract/msgpackr-extract-darwin-x64/-/msgpackr-extract-darwin-x64-3.0.3.tgz", "integrity": "sha512-mdzd3AVzYKuUmiWOQ8GNhl64/IoFGol569zNRdkLReh6LRLHOXxU4U8eq0JwaD8iFHdVGqSy4IjFL4reoWCDFw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@msgpackr-extract/msgpackr-extract-linux-arm": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/@msgpackr-extract/msgpackr-extract-linux-arm/-/msgpackr-extract-linux-arm-3.0.3.tgz", "integrity": "sha512-fg0uy/dG/nZEXfYilKoRe7yALaNmHoYeIoJuJ7KJ+YyU2bvY8vPv27f7UKhGRpY6euFYqEVhxCFZgAUNQBM3nw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@msgpackr-extract/msgpackr-extract-linux-arm64": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/@msgpackr-extract/msgpackr-extract-linux-arm64/-/msgpackr-extract-linux-arm64-3.0.3.tgz", "integrity": "sha512-YxQL+ax0XqBJDZiKimS2XQaf+2wDGVa1enVRGzEvLLVFeqa5kx2bWbtcSXgsxjQB7nRqqIGFIcLteF/sHeVtQg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@msgpackr-extract/msgpackr-extract-linux-x64": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/@msgpackr-extract/msgpackr-extract-linux-x64/-/msgpackr-extract-linux-x64-3.0.3.tgz", "integrity": "sha512-cvwNfbP07pKUfq1uH+S6KJ7dT9K8WOE4ZiAcsrSes+UY55E/0jLYc+vq+DO7jlmqRb5zAggExKm0H7O/CBaesg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@msgpackr-extract/msgpackr-extract-win32-x64": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/@msgpackr-extract/msgpackr-extract-win32-x64/-/msgpackr-extract-win32-x64-3.0.3.tgz", "integrity": "sha512-x0fWaQtYp4E6sktbsdAqnehxDgEc/VwM7uLsRCYWaiGu0ykYdZPiS8zCWdnjHwyiumousxfBm4SO31eXqwEZhQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@parcel/bundler-default": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/bundler-default/-/bundler-default-2.15.4.tgz", "integrity": "sha512-4vkaZuwGqL8L7NqEgjRznz9/QoeVKk0Z6z2nzfpdnSWA4xX3moUj+JeoqGUbyFGuPzfCma4SA4+txnQbKu0edQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/graph": "3.5.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/cache": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/cache/-/cache-2.15.4.tgz", "integrity": "sha512-x/QgMuVvXQV6uNhIF+6kz6SzhVVkwf6WPSVG/xQvGMEiBabForDVYIhIEuN3RzUXCU352CGM6d8TtLLg61W1fw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/fs": "2.15.4", "@parcel/logger": "2.15.4", "@parcel/utils": "2.15.4", "lmdb": "2.8.5"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/codeframe": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/codeframe/-/codeframe-2.15.4.tgz", "integrity": "sha512-ErAPEQaJIpB+ocNZ3rl8AEK6piA7JBInwZLNU0eHMthm01Ssb10JkpAadyn1w9IVfCey+kqQcEeWv47Yh6mL1Q==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.2"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/compressor-raw": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/compressor-raw/-/compressor-raw-2.15.4.tgz", "integrity": "sha512-gECePZxVXBwyo0DYbAq4V4SimVzHaJ3p8QOgFIfOqNmlEBbhLf3QSjArFPJNKiHZaJuclh4a+IShFBN+u6tXXw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/config-default": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/config-default/-/config-default-2.15.4.tgz", "integrity": "sha512-chUE4NpcSXpMfTcSmgl4Q78zH+ZFe0qdgZLBtF4EH2QQakW7wAXAYRxS2/P3xFkUj0/51sExhbCFWgulrlGDPw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/bundler-default": "2.15.4", "@parcel/compressor-raw": "2.15.4", "@parcel/namer-default": "2.15.4", "@parcel/optimizer-css": "2.15.4", "@parcel/optimizer-html": "2.15.4", "@parcel/optimizer-image": "2.15.4", "@parcel/optimizer-svg": "2.15.4", "@parcel/optimizer-swc": "2.15.4", "@parcel/packager-css": "2.15.4", "@parcel/packager-html": "2.15.4", "@parcel/packager-js": "2.15.4", "@parcel/packager-raw": "2.15.4", "@parcel/packager-svg": "2.15.4", "@parcel/packager-wasm": "2.15.4", "@parcel/reporter-dev-server": "2.15.4", "@parcel/resolver-default": "2.15.4", "@parcel/runtime-browser-hmr": "2.15.4", "@parcel/runtime-js": "2.15.4", "@parcel/runtime-rsc": "2.15.4", "@parcel/runtime-service-worker": "2.15.4", "@parcel/transformer-babel": "2.15.4", "@parcel/transformer-css": "2.15.4", "@parcel/transformer-html": "2.15.4", "@parcel/transformer-image": "2.15.4", "@parcel/transformer-js": "2.15.4", "@parcel/transformer-json": "2.15.4", "@parcel/transformer-node": "2.15.4", "@parcel/transformer-postcss": "2.15.4", "@parcel/transformer-posthtml": "2.15.4", "@parcel/transformer-raw": "2.15.4", "@parcel/transformer-react-refresh-wrap": "2.15.4", "@parcel/transformer-svg": "2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/core": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/core/-/core-2.15.4.tgz", "integrity": "sha512-+TXxTm58lFwXXObFAEclwKX1p1AdixcD+M7T4NeFIQzQ4F20Vr+6oybCSqW1exNA3uHqVDDFLx7TT78seVjvkg==", "dev": true, "license": "MIT", "dependencies": {"@mischnic/json-sourcemap": "^0.1.1", "@parcel/cache": "2.15.4", "@parcel/diagnostic": "2.15.4", "@parcel/events": "2.15.4", "@parcel/feature-flags": "2.15.4", "@parcel/fs": "2.15.4", "@parcel/graph": "3.5.4", "@parcel/logger": "2.15.4", "@parcel/package-manager": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/profiler": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/types": "2.15.4", "@parcel/utils": "2.15.4", "@parcel/workers": "2.15.4", "base-x": "^3.0.11", "browserslist": "^4.24.5", "clone": "^2.1.2", "dotenv": "^16.5.0", "dotenv-expand": "^11.0.7", "json5": "^2.2.3", "msgpackr": "^1.11.2", "nullthrows": "^1.1.1", "semver": "^7.7.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/diagnostic": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/diagnostic/-/diagnostic-2.15.4.tgz", "integrity": "sha512-8MAqefwzBKceNN3364OLm+p4HRD7AfimfFW3MntLxPB6bnelc9UBg5c9zEm34zYEctbmky8gqYgAUSDjqYC5Hw==", "dev": true, "license": "MIT", "dependencies": {"@mischnic/json-sourcemap": "^0.1.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/error-overlay": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/error-overlay/-/error-overlay-2.15.4.tgz", "integrity": "sha512-xxeaWm8fV8Z4uGy/c09mOvmFSHBOgF1gCMQwLCwZvfMLqIWkdZaUQ2cRhWZIS6pOXaRVC7YpcXzk2DOiSUNSbQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/events": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/events/-/events-2.15.4.tgz", "integrity": "sha512-SBq4zstaFr7XQaXNaQmUuVh1swCUHrhtPCOSofvkJoQGhjsuhQlh4t0NmUikyKNdj7C1j40xCS1kGHuUO29b0g==", "dev": true, "license": "MIT", "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/feature-flags": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/feature-flags/-/feature-flags-2.15.4.tgz", "integrity": "sha512-DJqZVtbfjWJseM0gk7yyDkAuOhP7/FVwZ/YVqjozIqXBhmQm07xctiqNQyZX2vBbQsxmVbjpqyq+DOj45WPEzQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/fs": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/fs/-/fs-2.15.4.tgz", "integrity": "sha512-5cahD2ByQaSi+YN0aDvrMWXZvs3mP7C5ey8zcDTDn7JxJa51sMqOQcdU3VUTzQFtAPeRM2KxUkxLhBBXgQqHZA==", "dev": true, "license": "MIT", "dependencies": {"@parcel/feature-flags": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/types-internal": "2.15.4", "@parcel/utils": "2.15.4", "@parcel/watcher": "^2.0.7", "@parcel/workers": "2.15.4"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/graph": {"version": "3.5.4", "resolved": "https://registry.npmmirror.com/@parcel/graph/-/graph-3.5.4.tgz", "integrity": "sha512-uF7kyQXWK2fQZvG5eE0N3avYGLQE5Q0vyJsyypNcFW3kXNnrkZCUtbG7urmdae9mmZ2jXIVN4q4Bhd9pefGj9A==", "dev": true, "license": "MIT", "dependencies": {"@parcel/feature-flags": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/logger": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/logger/-/logger-2.15.4.tgz", "integrity": "sha512-rQ7F5+FMQ7t+w5NGFRT8CWHhym0aunduufCjlafvRzUSKEN/5/nwTfCe9I5QsthGlXJWs+ZTy4zQ+wLtZQRBKQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/events": "2.15.4"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/markdown-ansi": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/markdown-ansi/-/markdown-ansi-2.15.4.tgz", "integrity": "sha512-u5Lwcr4ZVBSLFbKYht+mJqJ3ZMXvJdmDMU5eDtrIEKPpu9LrIDdPpDEXBoyO6pDsoV/2AqyXUUMzBRyCatkkoQ==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.2"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/namer-default": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/namer-default/-/namer-default-2.15.4.tgz", "integrity": "sha512-EXsoQ1S+5ZIfy8431E7F0vVS7bfH5JpZ+vFVcUpArJDkhmMG7T/eP6Kp9CXHLJmn7ki1x7iIVytrja0XXRQWBQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/node-resolver-core": {"version": "3.6.4", "resolved": "https://registry.npmmirror.com/@parcel/node-resolver-core/-/node-resolver-core-3.6.4.tgz", "integrity": "sha512-g3+usMnr7pfRqbMAksOpNA7GJk7HUNW1Wxx7Shhp4w0K9JUdVrd2LRKwZxbqL7H9NqWtVvUOT9cZbMlDR6bO1w==", "dev": true, "license": "MIT", "dependencies": {"@mischnic/json-sourcemap": "^0.1.1", "@parcel/diagnostic": "2.15.4", "@parcel/fs": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4", "nullthrows": "^1.1.1", "semver": "^7.7.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-css": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/optimizer-css/-/optimizer-css-2.15.4.tgz", "integrity": "sha512-KQLuqwcvVFTNFtM+bzfvQivwunmhVAngmR4NiI8zQaykidYH28V8YkVAQmpbLbgoGad/UgG7grb0UshvnrQHpw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4", "browserslist": "^4.24.5", "lightningcss": "^1.30.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-html": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/optimizer-html/-/optimizer-html-2.15.4.tgz", "integrity": "sha512-gBvt6RdDVMyO1Flvdtc8DxpxLgIXhaKuVXEjHdAP7sEW0SMdSd6r/tl6Plmcszig7sDwhDf6IsQOIvbzGHYZZg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-image": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/optimizer-image/-/optimizer-image-2.15.4.tgz", "integrity": "sha512-M8fo7eEL6JRcmLhSX9pUUGU4MPrPrE9cMNcwIt3DQLnSvQ+sshhUDa6t9hKWeHHhs16BHvxrvksN2TIbkgHODQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4", "@parcel/workers": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/optimizer-svg": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/optimizer-svg/-/optimizer-svg-2.15.4.tgz", "integrity": "sha512-pPdjRaLPqjAEROXIHLc6JWLLki56alhuUNbalhLqBCgktZrrq2dGCjBEVgxqRczc9D+ePCX/e/xci4tC0Tkcbg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-swc": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/optimizer-swc/-/optimizer-swc-2.15.4.tgz", "integrity": "sha512-2m5cYESVCq6AGx252eSTArZ1Oc1Ve4GBGL7NhvgbNqOthyXlc2qAed6rCkARrBd8pfEl5+2XHeK1ijDAZdIZ/A==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4", "@swc/core": "^1.11.24", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/package-manager": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/package-manager/-/package-manager-2.15.4.tgz", "integrity": "sha512-KZONBcEJ24moQdrpU0zJh9CYk3KKbpB5RUM70utAORem1yQKms+0Y4YED3njq6nZzbgwUN/Csc+powUHLZStvg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/fs": "2.15.4", "@parcel/logger": "2.15.4", "@parcel/node-resolver-core": "3.6.4", "@parcel/types": "2.15.4", "@parcel/utils": "2.15.4", "@parcel/workers": "2.15.4", "@swc/core": "^1.11.24", "semver": "^7.7.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/packager-css": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/packager-css/-/packager-css-2.15.4.tgz", "integrity": "sha512-bzSaNf+I5lmJFu95wSG2k7pGwjCDesZsV6Y9sozIL2LoSxqvkGhm/ABXAa3Ed7dLe3tSAEBzJcyqShQgLzSzuw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4", "lightningcss": "^1.30.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-html": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/packager-html/-/packager-html-2.15.4.tgz", "integrity": "sha512-Uayux6A2Anm66Kmq22QhD0TuVp9LiRCMuPUzBd6n4ekNlG0Lzm6K3/okMkPG65nKbNjq5qcPscFWlDxggvjt2g==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/types": "2.15.4", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-js": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/packager-js/-/packager-js-2.15.4.tgz", "integrity": "sha512-96bqhs1jyd28CfWQD+Yn8rSsd1ar7voHWyBtMLimsK+bDJIzL26Z7jWyRDwXRuLErYC01EoXRIRctxtmeRVJ2Q==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/types": "2.15.4", "@parcel/utils": "2.15.4", "globals": "^13.24.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-raw": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/packager-raw/-/packager-raw-2.15.4.tgz", "integrity": "sha512-CaSpDt5jjcO0SYCtsDhw6yfTDQuDFQ875H42W/ftvSQL7RfLRljPthnbdcy9chvKBbvRBQF+0z8Sxwehrd5hsA==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-svg": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/packager-svg/-/packager-svg-2.15.4.tgz", "integrity": "sha512-qHsyOgnzoA2XGMLIYUnX79XAaV327VTWQvIzju/OmOjcff4o3uiEcNL8w9k3p2w2oPXOLoQ0THMiivoUQSM8GQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/types": "2.15.4", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-wasm": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/packager-wasm/-/packager-wasm-2.15.4.tgz", "integrity": "sha512-YPVij7zrBchtXr/y29P4uh3C/+19PMhhLibYF/8oMJKkFkeU3Uv00/XLm915vdBPrIPjgw0YuIfLzUKip1uGtg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4"}, "engines": {"node": ">=16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/plugin": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/plugin/-/plugin-2.15.4.tgz", "integrity": "sha512-XVehjmzk8ZDOFf/BXo26L76ZqCGNKIQcN2ngxAnq0KRY/WFanL8yLaL0qQq+c9whlu09hkGz1CuhFBLAIjJMYQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/types": "2.15.4"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/profiler": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/profiler/-/profiler-2.15.4.tgz", "integrity": "sha512-ezVZlttUmQ1MQD5e8yVb07vSGYEFOB59Y/jaxL9mGSLZkVhMIIHe/7SuA+4qVAH8dlg6bslXRqlsunLMPEgPsg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/events": "2.15.4", "@parcel/types-internal": "2.15.4", "chrome-trace-event": "^1.0.2"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/reporter-cli": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/reporter-cli/-/reporter-cli-2.15.4.tgz", "integrity": "sha512-us0HIwuJqpSguf+yi4n8foabVs26JGvRB/eSOf0KkRldxFciYLn4NJ8rt3Xm1zvxlDiSkD4v2n77u+ouIZ+AEQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/types": "2.15.4", "@parcel/utils": "2.15.4", "chalk": "^4.1.2", "term-size": "^2.2.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/reporter-dev-server": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/reporter-dev-server/-/reporter-dev-server-2.15.4.tgz", "integrity": "sha512-uCNeDyArNNXI9YThlxyTx7+5ZSxlewyUdyrLdDZCqvn8s1xNB9W8sUNVps7mJZQSc+2ZRk3wyDemURD67uJk/A==", "dev": true, "license": "MIT", "dependencies": {"@parcel/codeframe": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/reporter-tracer": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/reporter-tracer/-/reporter-tracer-2.15.4.tgz", "integrity": "sha512-9W1xsb/FtobCQ4z847nI6hFDaTZHLeThv/z05EF77R30RX2k+unG9ac5NQB1v4KLx09Bhfre32+sjYNReWxWlg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4", "chrome-trace-event": "^1.0.3", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/resolver-default": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/resolver-default/-/resolver-default-2.15.4.tgz", "integrity": "sha512-4uKo3FFnubtIc4rM9jZiQQXpa1slawyRy5btJEfTFvbcnz0dm3WThLrsPDMfmPwNr9F/n5x8yzDLI6/fZ/elgA==", "dev": true, "license": "MIT", "dependencies": {"@parcel/node-resolver-core": "3.6.4", "@parcel/plugin": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-browser-hmr": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/runtime-browser-hmr/-/runtime-browser-hmr-2.15.4.tgz", "integrity": "sha512-KRGzbxDUOQUkrJKxxY0WyU7oVaa9TvWTRlpuGJXzQJs/hw8vkAAoAm8+ptpypvBC8LnxFHzGbSyHPfL8C8MQOw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-js": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/runtime-js/-/runtime-js-2.15.4.tgz", "integrity": "sha512-zNRK+693CMkYiA0ckjPOmz+JVHD9bVzp27itcMyuDH6l/Or8m09RgCC4DIdIxBqiplsDSe39DwEc5X7b0vvcjw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-rsc": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/runtime-rsc/-/runtime-rsc-2.15.4.tgz", "integrity": "sha512-yHc4HEwzCQYLqa6Q1WtZ8xJeaDAk0p2i0b3ABq2I+izmRjer4jertlsEwh9mf9Z1eUGtJobdGYzl8Ai1VfhC3g==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-service-worker": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/runtime-service-worker/-/runtime-service-worker-2.15.4.tgz", "integrity": "sha512-NGq/wS34GIVzo2ZURBjCqgHV+PU7eTcngCzmmk/wrCEeWnr13ld+CAIxVZoqyNJwYsF6VQanrjSM2/LhCXEdyA==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust/-/rust-2.15.4.tgz", "integrity": "sha512-OxOux8z8YEYg23+15uMmYaloFp3x1RwcliBay6HqxUW7RTmtI1/z+xd8AtienCckACD60gvDGy04LjgbEGdJVg==", "dev": true, "license": "MIT", "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/rust-darwin-arm64": "2.15.4", "@parcel/rust-darwin-x64": "2.15.4", "@parcel/rust-linux-arm-gnueabihf": "2.15.4", "@parcel/rust-linux-arm64-gnu": "2.15.4", "@parcel/rust-linux-arm64-musl": "2.15.4", "@parcel/rust-linux-x64-gnu": "2.15.4", "@parcel/rust-linux-x64-musl": "2.15.4", "@parcel/rust-win32-x64-msvc": "2.15.4"}, "peerDependencies": {"napi-wasm": "^1.1.2"}, "peerDependenciesMeta": {"napi-wasm": {"optional": true}}}, "node_modules/@parcel/rust-darwin-arm64": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-darwin-arm64/-/rust-darwin-arm64-2.15.4.tgz", "integrity": "sha512-cEpNDeEtvM5Nhj0QLN95QbcZ9yY6Z5W3+2OeHvnojEAP8Rp1XGzqVTTZdlyKyN1KTiyfzIOiQJCiEcr+kMc5Nw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-darwin-x64": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-darwin-x64/-/rust-darwin-x64-2.15.4.tgz", "integrity": "sha512-jL9i13sXKeBXXz8Z3BNYoScPOi+ljBA0ubAE3PN5DCoAA6wS4/FsAiRSIUw+3uxqASBD7+JvaT5sDUga1Xft5g==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-linux-arm-gnueabihf": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-linux-arm-gnueabihf/-/rust-linux-arm-gnueabihf-2.15.4.tgz", "integrity": "sha512-c8HpVdDugCutlMILoOlkTioih9HGJpQrzS2G3cg/O1a5ZTacooGf3eGJGoh6dUBEv9WEaEb6zsTRwFv2BgtZcA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-linux-arm64-gnu": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-linux-arm64-gnu/-/rust-linux-arm64-gnu-2.15.4.tgz", "integrity": "sha512-Wcfs/JY4FnuLxQaU+VX2rI4j376Qo2LkZmq4zp9frnsajaAqmloVQfnbUkdnQPEL4I38eHXerzBX3LoXSxnZKA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-linux-arm64-musl": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-linux-arm64-musl/-/rust-linux-arm64-musl-2.15.4.tgz", "integrity": "sha512-xf9HxosEn3dU5M0zDSXqBaG8rEjLThRdTYqpkxHW/qQGzy0Se+/ntg8PeDHsSG5E9OK8xrcKH46Lhaw0QBF/Zw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-linux-x64-gnu": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-linux-x64-gnu/-/rust-linux-x64-gnu-2.15.4.tgz", "integrity": "sha512-RigXVCFj6h0AXmkuxU61rfgYuW+PXBR6qSkR2I20yKnAXoMfxLaZy9YJ3sAPMEjT9zXgzGAX+3syItMF+bRjaw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-linux-x64-musl": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-linux-x64-musl/-/rust-linux-x64-musl-2.15.4.tgz", "integrity": "sha512-tHlRgonSr5ca8OvhbGzZUggCgCOirRz5dHhPSCm4ajMxeDMamwprq6lKy0sCNTXht4TXIEyugBcfEuRKEeVIBw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/rust-win32-x64-msvc": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/rust-win32-x64-msvc/-/rust-win32-x64-msvc-2.15.4.tgz", "integrity": "sha512-YsX6vMl/bfyxqZSN7yiaZQKLoJKELSZYcvg8gIv4CF1xkaTdmfr6gvq2iCyoV+bwrodNohN4Xfl8r7Wniu1/UA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/source-map": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/@parcel/source-map/-/source-map-2.1.1.tgz", "integrity": "sha512-Ejx1P/mj+kMjQb8/y5XxDUn4reGdr+WyKYloBljpppUy8gs42T+BNoEOuRYqDVdgPc6NxduzIDoJS9pOFfV5Ew==", "dev": true, "license": "MIT", "dependencies": {"detect-libc": "^1.0.3"}, "engines": {"node": "^12.18.3 || >=14"}}, "node_modules/@parcel/transformer-babel": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-babel/-/transformer-babel-2.15.4.tgz", "integrity": "sha512-rb4nqZcTLkLD3nvuYJ9wwNb8x6cajBK2l6csdYMLEI4516SkIzkO/gs2cZ9M5q+CMhxAqpdEnrwektbOtQQasg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4", "browserslist": "^4.24.5", "json5": "^2.2.3", "nullthrows": "^1.1.1", "semver": "^7.7.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-css": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-css/-/transformer-css-2.15.4.tgz", "integrity": "sha512-6tVwSJsOssXgcB5XMAQGsexAffoBEi8GVql3YQqzI1EwVYs9zr+B5mfbesb4aWcegR02w99NHJYFP9CrOr3SWw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4", "browserslist": "^4.24.5", "lightningcss": "^1.30.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-html": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-html/-/transformer-html-2.15.4.tgz", "integrity": "sha512-gzYPbbyEuV8nzPojw86eD5Kf93AYUWcY8lu33gu0XHROJH7mq5MAwPwtb/U+EfpeCd0/oKbLzA2mkQksM1NncQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-image": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-image/-/transformer-image-2.15.4.tgz", "integrity": "sha512-KOVwj2gKjUybuzHwarC/YVqRf3r2BD4/2ysckozj6DIji/bq3fd2rE9yqxWXO+zt918PsOSTzMKwRnaseaXLKQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4", "@parcel/workers": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/transformer-js": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-js/-/transformer-js-2.15.4.tgz", "integrity": "sha512-HX76PalPjqCLmXJnuSeMr2km8WlnUsW8oaRZ6FuZtSo9QD8BqIcwKGxSbIy9JHkObBgmrMOVpGtYrJM4/BlYbg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.4", "@parcel/workers": "2.15.4", "@swc/helpers": "^0.5.0", "browserslist": "^4.24.5", "nullthrows": "^1.1.1", "regenerator-runtime": "^0.14.1", "semver": "^7.7.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@parcel/transformer-json": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-json/-/transformer-json-2.15.4.tgz", "integrity": "sha512-1ASeOSH3gPeaXyy/TZ7ce2TOfJ3ZeK5SBnDs+MM8LFcQsTwdRJKjX/4Qq9RgtMRryYAGHgMa09Gvp9FuFRyd+w==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "json5": "^2.2.3"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-node": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-node/-/transformer-node-2.15.4.tgz", "integrity": "sha512-zV5jvZA971eQMcFtaWZkW1UfAH/G6XVM/87oJ2B4ip9o9aKUWIl296rrfg2xWxUQyPhy11B17CJ6b8NgieqqrQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-postcss": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-postcss/-/transformer-postcss-2.15.4.tgz", "integrity": "sha512-cNueSpOj3ulmMX85xr9clh/t0+mzVE+Q3H7Cf/OammqUkG/xjmilq4q7ZTgQFyUtUdWpE9LWWHojbJuz6k2Ulw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/utils": "2.15.4", "clone": "^2.1.2", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "semver": "^7.7.1"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-posthtml": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-posthtml/-/transformer-posthtml-2.15.4.tgz", "integrity": "sha512-dETI+CeKMwu5Dpvu8BrQtex6nwzbNWKQkXseiM5x6+Wf3j9RD2NVpAMBRMjLkw1XlC9Whz1egxLSgKlMKbjg0w==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-raw": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-raw/-/transformer-raw-2.15.4.tgz", "integrity": "sha512-pY2j09UCW2v1fwQtVLlCztSdPOxhq0YcWmTHCk/mRp8zuUR+eyHgsz48FrUxRF7cr/EBjc0zlFcregRMRcaTMg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/plugin": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-react-refresh-wrap": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-react-refresh-wrap/-/transformer-react-refresh-wrap-2.15.4.tgz", "integrity": "sha512-MgoQrV8+BVjrczAns5ZZbTERGB3/U4MaCBmbg3CuiTiIyS8IJQnGi+OhYRdKAB4NlsgpMZ5T2JrRbQUIm9MM8Q==", "dev": true, "license": "MIT", "dependencies": {"@parcel/error-overlay": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/utils": "2.15.4", "react-refresh": "^0.16.0"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-svg": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/transformer-svg/-/transformer-svg-2.15.4.tgz", "integrity": "sha512-Q22e0VRbx62VXFlvJWIlc8ihlLaPQgtnAZz5E1/+ojiNb+k0PmIRjNJclVWPF6IdCsLO5tnGfUOaXe2OnZz28Q==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/plugin": "2.15.4", "@parcel/rust": "2.15.4"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/types": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/types/-/types-2.15.4.tgz", "integrity": "sha512-fS3UMMinLtzn/NTSx/qx38saBgRniylldh0XZEUcGeME4D2Llu/QlLv+YZ/LJqrFci3fPRM+YAn2K+JT/u+/0w==", "dev": true, "license": "MIT", "dependencies": {"@parcel/types-internal": "2.15.4", "@parcel/workers": "2.15.4"}}, "node_modules/@parcel/types-internal": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/types-internal/-/types-internal-2.15.4.tgz", "integrity": "sha512-kl5QEZ8PTWRvMkwmk7IG3VpP/5/MSGwt9Nrj9ctXLdZkDdXZpK7IbXAthLQ4zrByMaqZULL2IyDuBqBgfuAqlQ==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/feature-flags": "2.15.4", "@parcel/source-map": "^2.1.1", "utility-types": "^3.11.0"}}, "node_modules/@parcel/utils": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/utils/-/utils-2.15.4.tgz", "integrity": "sha512-29m09sfPx0GHnmy1kkZ5XezprepdFGKKKUEJkyiYA4ERf55jjdnU2/GP4sWlZXxjh2Y+JFoCAFlCamEClq/8eA==", "dev": true, "license": "MIT", "dependencies": {"@parcel/codeframe": "2.15.4", "@parcel/diagnostic": "2.15.4", "@parcel/logger": "2.15.4", "@parcel/markdown-ansi": "2.15.4", "@parcel/rust": "2.15.4", "@parcel/source-map": "^2.1.1", "chalk": "^4.1.2", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.5.1.tgz", "integrity": "sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "node_modules/@parcel/watcher-android-arm64": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-android-arm64/-/watcher-android-arm64-2.5.1.tgz", "integrity": "sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-darwin-arm64": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-darwin-arm64/-/watcher-darwin-arm64-2.5.1.tgz", "integrity": "sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-darwin-x64": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-darwin-x64/-/watcher-darwin-x64-2.5.1.tgz", "integrity": "sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-freebsd-x64": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.1.tgz", "integrity": "sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm-glibc": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.1.tgz", "integrity": "sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm-musl": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-musl/-/watcher-linux-arm-musl-2.5.1.tgz", "integrity": "sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm64-glibc": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.1.tgz", "integrity": "sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm64-musl": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.1.tgz", "integrity": "sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-x64-glibc": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz", "integrity": "sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-x64-musl": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz", "integrity": "sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-win32-arm64": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.1.tgz", "integrity": "sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-win32-ia32": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.1.tgz", "integrity": "sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-win32-x64": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz", "integrity": "sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/workers": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/@parcel/workers/-/workers-2.15.4.tgz", "integrity": "sha512-wZ/5/mfjs5aeqhXY0c6fwuaBFeNpOXoOq2CKPSMDXt+GX2u/9/1bpVxN9XeGTAJO+ZD++CLq0hyzTnIHy58nyw==", "dev": true, "license": "MIT", "dependencies": {"@parcel/diagnostic": "2.15.4", "@parcel/logger": "2.15.4", "@parcel/profiler": "2.15.4", "@parcel/types-internal": "2.15.4", "@parcel/utils": "2.15.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.15.4"}}, "node_modules/@react-dnd/asap": {"version": "5.0.2", "resolved": "https://registry.npmmirror.com/@react-dnd/asap/-/asap-5.0.2.tgz", "integrity": "sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A==", "license": "MIT"}, "node_modules/@react-dnd/invariant": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/@react-dnd/invariant/-/invariant-4.0.2.tgz", "integrity": "sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw==", "license": "MIT"}, "node_modules/@react-dnd/shallowequal": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/@react-dnd/shallowequal/-/shallowequal-4.0.2.tgz", "integrity": "sha512-/RVXdLvJxLg4QKvMoM5WlwNR9ViO9z8B/qPcc+C0Sa/teJY7QG7kJ441DwzOjMYEY7GmU4dj5EcGHIkKZiQZCA==", "license": "MIT"}, "node_modules/@swc/core": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core/-/core-1.13.1.tgz", "integrity": "sha512-jEKKErLC6uwSqA+p6bmZR08usZM5Fpc+HdEu5CAzvye0q43yf1si1kjhHEa9XMkz0A2SAaal3eKCg/YYmtOsCA==", "dev": true, "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"@swc/counter": "^0.1.3", "@swc/types": "^0.1.23"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/swc"}, "optionalDependencies": {"@swc/core-darwin-arm64": "1.13.1", "@swc/core-darwin-x64": "1.13.1", "@swc/core-linux-arm-gnueabihf": "1.13.1", "@swc/core-linux-arm64-gnu": "1.13.1", "@swc/core-linux-arm64-musl": "1.13.1", "@swc/core-linux-x64-gnu": "1.13.1", "@swc/core-linux-x64-musl": "1.13.1", "@swc/core-win32-arm64-msvc": "1.13.1", "@swc/core-win32-ia32-msvc": "1.13.1", "@swc/core-win32-x64-msvc": "1.13.1"}, "peerDependencies": {"@swc/helpers": ">=0.5.17"}, "peerDependenciesMeta": {"@swc/helpers": {"optional": true}}}, "node_modules/@swc/core-darwin-arm64": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-darwin-arm64/-/core-darwin-arm64-1.13.1.tgz", "integrity": "sha512-zO6SW/jSMTUORPm6dUZFPUwf+EFWZsaXWMGXadRG6akCofYpoQb8pcY2QZkVr43z8TMka6BtXpyoD/DJ0iOPHQ==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-darwin-x64": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-darwin-x64/-/core-darwin-x64-1.13.1.tgz", "integrity": "sha512-8RjaTZYxrlYKE5PgzZYWSOT4mAsyhIuh30Nu4dnn/2r0Ef68iNCbvX4ynGnFMhOIhqunjQbJf+mJKpwTwdHXhw==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-linux-arm-gnueabihf": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.13.1.tgz", "integrity": "sha512-jEqK6pECs2m4BpL2JA/4CCkq04p6iFOEtVNXTisO+lJ3zwmxlnIEm9UfJZG6VSu8GS9MHRKGB0ieZ1tEdN1qDA==", "cpu": ["arm"], "dev": true, "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-linux-arm64-gnu": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.13.1.tgz", "integrity": "sha512-PbkuIOYXO/gQbWQ7NnYIwm59ygNqmUcF8LBeoKvxhx1VtOwE+9KiTfoplOikkPLhMiTzKsd8qentTslbITIg+Q==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-linux-arm64-musl": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.13.1.tgz", "integrity": "sha512-JaqFdBCarIBKiMu5bbAp+kWPMNGg97ej+7KzbKOzWP5pRptqKi86kCDZT3WmjPe8hNG6dvBwbm7Y8JNry5LebQ==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-linux-x64-gnu": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.13.1.tgz", "integrity": "sha512-t4cLkku10YECDaakWUH0452WJHIZtrLPRwezt6BdoMntVMwNjvXRX7C8bGuYcKC3YxRW7enZKFpozLhQIQ37oA==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-linux-x64-musl": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.13.1.tgz", "integrity": "sha512-fSMwZOaG+3ukUucbEbzz9GhzGhUhXoCPqHe9qW0/Vc2IZRp538xalygKyZynYweH5d9EHux1aj3+IO8/xBaoiA==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-win32-arm64-msvc": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.13.1.tgz", "integrity": "sha512-tweCXK/79vAwj1NhAsYgICy8T1z2QEairmN2BFEBYFBFNMEB1iI1YlXwBkBtuihRvgZrTh1ORusKa4jLYzLCZA==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-win32-ia32-msvc": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.13.1.tgz", "integrity": "sha512-zi7hO9D+2R2yQN9D7T10/CAI9KhuXkNkz8tcJOW6+dVPtAk/gsIC5NoGPELjgrAlLL9CS38ZQpLDslLfpP15ng==", "cpu": ["ia32"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=10"}}, "node_modules/@swc/core-win32-x64-msvc": {"version": "1.13.1", "resolved": "https://registry.npmmirror.com/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.13.1.tgz", "integrity": "sha512-KubYjzqs/nz3H69ncX/XHKsC8c1xqc7UvonQAj26BhbL22HBsqdAaVutZ+Obho6RMpd3F5qQ95ldavUTWskRrw==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=10"}}, "node_modules/@swc/counter": {"version": "0.1.3", "resolved": "https://registry.npmmirror.com/@swc/counter/-/counter-0.1.3.tgz", "integrity": "sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==", "dev": true, "license": "Apache-2.0"}, "node_modules/@swc/helpers": {"version": "0.5.17", "resolved": "https://registry.npmmirror.com/@swc/helpers/-/helpers-0.5.17.tgz", "integrity": "sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@swc/types": {"version": "0.1.23", "resolved": "https://registry.npmmirror.com/@swc/types/-/types-0.1.23.tgz", "integrity": "sha512-u1iIVZV9Q0jxY+yM2vw/hZGDNudsN85bBpTqzAQ9rzkxW9D+e3aEM4Han+ow518gSewkXgjmEK0BD79ZcNVgPw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@swc/counter": "^0.1.3"}}, "node_modules/@types/asap": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/@types/asap/-/asap-2.0.2.tgz", "integrity": "sha512-MSz66ws9c6lTtE6MGaNFHMMLscRusdKswvT+bO97Uk9giC9OE+lBBxQyvgt0RrC/Sm+qgJHRLfrdfw5Hr/ZYbg==", "license": "MIT"}, "node_modules/@types/hoist-non-react-statics": {"version": "3.3.7", "resolved": "https://registry.npmmirror.com/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.7.tgz", "integrity": "sha512-PQTyIulDkIDro8P+IHbKCsw7U2xxBYflVzW/FgWdCAePD9xGSidgA76/GeJ6lBKoblyhf9pBY763gbrN+1dI8g==", "license": "MIT", "optional": true, "peer": true, "dependencies": {"hoist-non-react-statics": "^3.3.0"}, "peerDependencies": {"@types/react": "*"}}, "node_modules/@types/invariant": {"version": "2.2.37", "resolved": "https://registry.npmmirror.com/@types/invariant/-/invariant-2.2.37.tgz", "integrity": "sha512-IwpIMieE55oGWiXkQPSBY1nw1nFs6bsKXTFskNY8sdS17K24vyEBRQZEwlRS7ZmXCWnJcQtbxWzly+cODWGs2A==", "license": "MIT"}, "node_modules/@types/prop-types": {"version": "15.7.15", "resolved": "https://registry.npmmirror.com/@types/prop-types/-/prop-types-15.7.15.tgz", "integrity": "sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==", "license": "MIT"}, "node_modules/@types/react": {"version": "17.0.87", "resolved": "https://registry.npmmirror.com/@types/react/-/react-17.0.87.tgz", "integrity": "sha512-wpg9AbtJ6agjA+BKYmhG6dRWEU/2DHYwMzCaBzsz137ft6IyuqZ5fI4ic1DWL4DrI03Zy78IyVE6ucrXl0mu4g==", "license": "MIT", "dependencies": {"@types/prop-types": "*", "@types/scheduler": "^0.16", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "17.0.26", "resolved": "https://registry.npmmirror.com/@types/react-dom/-/react-dom-17.0.26.tgz", "integrity": "sha512-Z+2VcYXJwOqQ79HreLU/1fyQ88eXSSFh6I3JdrEHQIfYSI0kCQpTGvOrbE6jFGGYXKsHuwY9tBa/w5Uo6KzrEg==", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^17.0.0"}}, "node_modules/@types/react-slick": {"version": "0.23.13", "resolved": "https://registry.npmmirror.com/@types/react-slick/-/react-slick-0.23.13.tgz", "integrity": "sha512-bNZfDhe/L8t5OQzIyhrRhBr/61pfBcWaYJoq6UDqFtv5LMwfg4NsVDD2J8N01JqdAdxLjOt66OZEp6PX+dGs/A==", "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/scheduler": {"version": "0.16.8", "resolved": "https://registry.npmmirror.com/@types/scheduler/-/scheduler-0.16.8.tgz", "integrity": "sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==", "license": "MIT"}, "node_modules/add-dom-event-listener": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz", "integrity": "sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw==", "license": "MIT", "dependencies": {"object-assign": "4.x"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/antd": {"version": "3.26.20", "resolved": "https://registry.npmmirror.com/antd/-/antd-3.26.20.tgz", "integrity": "sha512-VIous4ofZfxFtd9K1h9MpRX2sDDpj3QcOFi3YgIc9B/uyDli/GlLb8SWKfQfJaMkaxwatIv503dag2Tog+hiEg==", "license": "MIT", "dependencies": {"@ant-design/create-react-context": "^0.2.4", "@ant-design/icons": "~2.1.1", "@ant-design/icons-react": "~2.0.1", "@types/react-slick": "^0.23.4", "array-tree-filter": "^2.1.0", "babel-runtime": "6.x", "classnames": "~2.2.6", "copy-to-clipboard": "^3.2.0", "css-animation": "^1.5.0", "dom-closest": "^0.2.0", "enquire.js": "^2.1.6", "is-mobile": "^2.1.0", "lodash": "^4.17.13", "moment": "^2.24.0", "omit.js": "^1.0.2", "prop-types": "^15.7.2", "raf": "^3.4.1", "rc-animate": "^2.10.2", "rc-calendar": "~9.15.7", "rc-cascader": "~0.17.4", "rc-checkbox": "~2.1.6", "rc-collapse": "~1.11.3", "rc-dialog": "~7.6.0", "rc-drawer": "~3.1.1", "rc-dropdown": "~2.4.1", "rc-editor-mention": "^1.1.13", "rc-form": "^2.4.10", "rc-input-number": "~4.5.0", "rc-mentions": "~0.4.0", "rc-menu": "~7.5.1", "rc-notification": "~3.3.1", "rc-pagination": "~1.20.11", "rc-progress": "~2.5.0", "rc-rate": "~2.5.0", "rc-resize-observer": "^0.1.0", "rc-select": "~9.2.0", "rc-slider": "~8.7.1", "rc-steps": "~3.5.0", "rc-switch": "~1.9.0", "rc-table": "~6.10.5", "rc-tabs": "~9.7.0", "rc-time-picker": "~3.7.1", "rc-tooltip": "~3.7.3", "rc-tree": "~2.1.0", "rc-tree-select": "~2.9.1", "rc-trigger": "^2.6.2", "rc-upload": "~2.9.1", "rc-util": "^4.16.1", "react-lazy-load": "^3.0.13", "react-lifecycles-compat": "^3.0.4", "react-slick": "~0.25.2", "resize-observer-polyfill": "^1.5.1", "shallowequal": "^1.1.0", "warning": "~4.0.3"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}}, "node_modules/antd/node_modules/@ant-design/icons-react": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/@ant-design/icons-react/-/icons-react-2.0.1.tgz", "integrity": "sha512-r1QfoltMuruJZqdiKcbPim3d8LNsVPB733U0gZEUSxBLuqilwsW28K2rCTWSMTjmFX7Mfpf+v/wdiFe/XCqThw==", "license": "MIT", "dependencies": {"@ant-design/colors": "^3.1.0", "babel-runtime": "^6.26.0"}, "peerDependencies": {"@ant-design/icons": "^2.0.0", "react": "16.x"}}, "node_modules/antd/node_modules/rc-resize-observer": {"version": "0.1.3", "resolved": "https://registry.npmmirror.com/rc-resize-observer/-/rc-resize-observer-0.1.3.tgz", "integrity": "sha512-uzOQEwx83xdQSFOkOAM7x7GHIQKYnrDV4dWxtCxyG1BS1pkfJ4EvDeMfsvAJHSYkQXVBu+sgRHGbRtLG3qiuUg==", "license": "MIT", "dependencies": {"classnames": "^2.2.1", "rc-util": "^4.13.0", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": "^16.0.0", "react-dom": "^16.0.0"}}, "node_modules/antd/node_modules/rc-switch": {"version": "1.9.2", "resolved": "https://registry.npmmirror.com/rc-switch/-/rc-switch-1.9.2.tgz", "integrity": "sha512-qaK7mY4FLDKy99Hq3A1tf8CcqfzKtHp9LPX8WTnZ0MzdHCTneSARb1XD7Eqeu8BactasYGsi2bF9p18Q+/5JEw==", "license": "MIT", "dependencies": {"classnames": "^2.2.1", "prop-types": "^15.5.6", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": "^16.0.0", "react-dom": "^16.0.0"}}, "node_modules/antd/node_modules/rc-table": {"version": "6.10.15", "resolved": "https://registry.npmmirror.com/rc-table/-/rc-table-6.10.15.tgz", "integrity": "sha512-LAr0M/gqt+irOjvPNBLApmQ0CUHNOfKsEBhu1uIuB3OlN1ynA9z+sdoTQyNd9+8NSl0MYnQOOfhtLChAY7nU0A==", "license": "MIT", "dependencies": {"classnames": "^2.2.5", "component-classes": "^1.2.6", "lodash": "^4.17.5", "mini-store": "^2.0.0", "prop-types": "^15.5.8", "rc-util": "^4.13.0", "react-lifecycles-compat": "^3.0.2", "shallowequal": "^1.0.2"}, "peerDependencies": {"react": "^16.0.0", "react-dom": "^16.0.0"}}, "node_modules/antd/node_modules/react-lazy-load": {"version": "3.1.14", "resolved": "https://registry.npmmirror.com/react-lazy-load/-/react-lazy-load-3.1.14.tgz", "integrity": "sha512-7tsOItf2HmEwhEWMaA/a2XlShuya7rBxqWAR0TPMO1XSf6ybxSDI2bMV8M6vtWkveX9TlSpb0qLB7NMMpDHVDQ==", "license": "MIT", "dependencies": {"eventlistener": "0.0.1", "lodash.debounce": "^4.0.0", "lodash.throttle": "^4.0.0", "prop-types": "^15.5.8"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-0 || ^16.0.0 || ^17.0.0", "react-dom": "^0.14.0 || ^15.0.0-0 || ^16.0.0 || ^17.0.0"}}, "node_modules/antd/node_modules/react-slick": {"version": "0.25.2", "resolved": "https://registry.npmmirror.com/react-slick/-/react-slick-0.25.2.tgz", "integrity": "sha512-8MNH/NFX/R7zF6W/w+FS5VXNyDusF+XDW1OU0SzODEU7wqYB+ZTGAiNJ++zVNAVqCAHdyCybScaUB+FCZOmBBw==", "license": "MIT", "dependencies": {"classnames": "^2.2.5", "enquire.js": "^2.1.6", "json2mq": "^0.2.0", "lodash.debounce": "^4.0.8", "resize-observer-polyfill": "^1.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.1 || ^16.0.0", "react-dom": "^0.14.0 || ^15.0.1 || ^16.0.0"}}, "node_modules/array-tree-filter": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/array-tree-filter/-/array-tree-filter-2.1.0.tgz", "integrity": "sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==", "license": "MIT"}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://registry.npmmirror.com/asap/-/asap-2.0.6.tgz", "integrity": "sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==", "license": "MIT"}, "node_modules/async-validator": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.5.tgz", "integrity": "sha512-XNtCsMAeAH1pdLMEg1z8/Bb3a8cdCbui9QbJATRFHHHW5kT6+NPI3zSVQUXgikTFITzsg+kYY5NTWhM2Orwt9w=="}, "node_modules/babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmmirror.com/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==", "license": "MIT", "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "node_modules/babel-runtime/node_modules/regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==", "license": "MIT"}, "node_modules/base-x": {"version": "3.0.11", "resolved": "https://registry.npmmirror.com/base-x/-/base-x-3.0.11.tgz", "integrity": "sha512-xz7wQ8xDhdyP7tQxwdteLYeFfS68tSMNCZ/Y37WJ4bhGfKPpqEIlmIyueQHqOyoPhE6xNUqjzRr8ra0eF9VRvA==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "https://registry.npmmirror.com/browserslist/-/browserslist-4.25.1.tgz", "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/caniuse-lite": {"version": "1.0.30001727", "resolved": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "integrity": "sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz", "integrity": "sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/classnames": {"version": "2.2.6", "resolved": "https://registry.npmmirror.com/classnames/-/classnames-2.2.6.tgz", "integrity": "sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q==", "license": "MIT"}, "node_modules/clone": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz", "integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "12.1.0", "resolved": "https://registry.npmmirror.com/commander/-/commander-12.1.0.tgz", "integrity": "sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/component-classes": {"version": "1.2.6", "resolved": "https://registry.npmmirror.com/component-classes/-/component-classes-1.2.6.tgz", "integrity": "sha512-hPFGULxdwugu1QWW3SvVOCUHLzO34+a2J6Wqy0c5ASQkfi9/8nZcBB0ZohaEbXOQlCflMAEMmEWk7u7BVs4koA==", "license": "MIT", "dependencies": {"component-indexof": "0.0.3"}}, "node_modules/component-indexof": {"version": "0.0.3", "resolved": "https://registry.npmmirror.com/component-indexof/-/component-indexof-0.0.3.tgz", "integrity": "sha512-puDQKvx/64HZXb4hBwIcvQLaLgux8o1CbWl39s41hrIIZDl1lJiD5jc22gj3RBeGK0ovxALDYpIbyjqDUUl0rw=="}, "node_modules/copy-to-clipboard": {"version": "3.3.3", "resolved": "https://registry.npmmirror.com/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz", "integrity": "sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==", "license": "MIT", "dependencies": {"toggle-selection": "^1.0.6"}}, "node_modules/core-js": {"version": "2.6.12", "resolved": "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz", "integrity": "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "hasInstallScript": true, "license": "MIT"}, "node_modules/create-react-class": {"version": "15.7.0", "resolved": "https://registry.npmmirror.com/create-react-class/-/create-react-class-15.7.0.tgz", "integrity": "sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng==", "license": "MIT", "dependencies": {"loose-envify": "^1.3.1", "object-assign": "^4.1.1"}}, "node_modules/css-animation": {"version": "1.6.1", "resolved": "https://registry.npmmirror.com/css-animation/-/css-animation-1.6.1.tgz", "integrity": "sha512-/48+/BaEaHRY6kNQ2OIPzKf9A6g8WjZYjhiNDNuIVbsm5tXCGIAsHDjB4Xu1C4vXJtUWZo26O68OQkDpNBaPog==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "component-classes": "^1.2.5"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "license": "MIT"}, "node_modules/detect-libc": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/detect-libc/-/detect-libc-1.0.3.tgz", "integrity": "sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==", "dev": true, "license": "Apache-2.0", "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/dnd-core": {"version": "16.0.1", "resolved": "https://registry.npmmirror.com/dnd-core/-/dnd-core-16.0.1.tgz", "integrity": "sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng==", "license": "MIT", "dependencies": {"@react-dnd/asap": "^5.0.1", "@react-dnd/invariant": "^4.0.1", "redux": "^4.2.0"}}, "node_modules/dom-align": {"version": "1.12.4", "resolved": "https://registry.npmmirror.com/dom-align/-/dom-align-1.12.4.tgz", "integrity": "sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==", "license": "MIT"}, "node_modules/dom-closest": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/dom-closest/-/dom-closest-0.2.0.tgz", "integrity": "sha512-6neTn1BtJlTSt+XSISXpnOsF1uni1CHsP/tmzZMGWxasYFHsBOqrHPnzmneqEgKhpagnfnfSfbvRRW0xFsBHAA==", "license": "MIT", "dependencies": {"dom-matches": ">=1.0.1"}}, "node_modules/dom-matches": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/dom-matches/-/dom-matches-2.0.0.tgz", "integrity": "sha512-2VI856xEDCLXi19W+4BechR5/oIS6bKCKqcf16GR8Pg7dGLJ/eBOWVbCmQx2ISvYH6wTNx5Ef7JTOw1dRGRx6A==", "license": "MIT"}, "node_modules/dom-scroll-into-view": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/dom-scroll-into-view/-/dom-scroll-into-view-1.2.1.tgz", "integrity": "sha512-LwNVg3GJOprWDO+QhLL1Z9MMgWe/KAFLxVWKzjRTxNSPn8/LLDIfmuG71YHznXCqaqTjvHJDYO1MEAgX6XCNbQ==", "license": "MIT"}, "node_modules/dotenv": {"version": "16.6.1", "resolved": "https://registry.npmmirror.com/dotenv/-/dotenv-16.6.1.tgz", "integrity": "sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dotenv-expand": {"version": "11.0.7", "resolved": "https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-11.0.7.tgz", "integrity": "sha512-zIHwmZPRshsCdpMDyVsqGmgyP0yT8GAgXUnkdAoJisxvf33k7yO6OuoKmcTGuXPWSsm8Oh88nZicRLA9Y0rUeA==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dotenv": "^16.4.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/electron-to-chromium": {"version": "1.5.187", "resolved": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.187.tgz", "integrity": "sha512-cl5Jc9I0KGUoOoSbxvTywTa40uspGJt/BDBoDLoxJRSBpWh4FFXBsjNRHfQrONsV/OoEjDfHUmZQa2d6Ze4YgA==", "dev": true, "license": "ISC"}, "node_modules/encoding": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "license": "MIT", "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/enquire.js": {"version": "2.1.6", "resolved": "https://registry.npmmirror.com/enquire.js/-/enquire.js-2.1.6.tgz", "integrity": "sha512-/KujNpO+PT63F7Hlpu4h3pE3TokKRHN26JYmQpPyjkRD/N57R7bPDNojMXdi7uveAKjYB7yQnartCxZnFWr0Xw==", "license": "MIT"}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eventlistener": {"version": "0.0.1", "resolved": "https://registry.npmmirror.com/eventlistener/-/eventlistener-0.0.1.tgz", "integrity": "sha512-hXZ5N9hmp3n7ovmVgG+2vIO6KcjSU10/d0A1Ixcf0i29dxCwAGTNGrSJCfLmlvmgQD8FYzyp//S8+Hpq4Nd7uA==", "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "license": "MIT"}, "node_modules/fbjs": {"version": "0.8.18", "resolved": "https://registry.npmmirror.com/fbjs/-/fbjs-0.8.18.tgz", "integrity": "sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA==", "license": "MIT", "dependencies": {"core-js": "^1.0.0", "isomorphic-fetch": "^2.1.1", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^0.7.30"}}, "node_modules/fbjs/node_modules/core-js": {"version": "1.2.7", "resolved": "https://registry.npmmirror.com/core-js/-/core-js-1.2.7.tgz", "integrity": "sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "license": "MIT"}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/get-port": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/get-port/-/get-port-4.2.0.tgz", "integrity": "sha512-/b3jarXkH8KJoOMQc3uVGHASwGLPq3gSFJ7tgJm2diza+bydJPTGOibin2steecKeOylE8oY2JERlVWkAJO6yw==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/globals": {"version": "13.24.0", "resolved": "https://registry.npmmirror.com/globals/-/globals-13.24.0.tgz", "integrity": "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gud": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/gud/-/gud-1.0.0.tgz", "integrity": "sha512-zGEOVKFM5sVPPrYs7J5/hYEw2Pof8KCyOwyhG8sAF26mCAeUFAcYPu1mwB7hhpIP29zOIBaDqwuHdLp0jvZXjw==", "license": "MIT"}, "node_modules/hammerjs": {"version": "2.0.8", "resolved": "https://registry.npmmirror.com/hammerjs/-/hammerjs-2.0.8.tgz", "integrity": "sha512-tSQXBXS/MWQOn/RKckawJ61vvsDpCom87JgxiYdGwHdOa0ht0vzUWDlfioofFCRU0L+6NGDt6XzbgoJvZkMeRQ==", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "resolved": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "integrity": "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/immutable": {"version": "3.7.6", "resolved": "https://registry.npmmirror.com/immutable/-/immutable-3.7.6.tgz", "integrity": "sha512-AizQPcaofEtO11RZhPPHBOJRdo/20MKQF9mBLnVkBoyHi1/zXK8fzVdnEpSV9gxqtnh6Qomfp3F0xT5qP/vThw==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.8.0"}}, "node_modules/insert-css": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/insert-css/-/insert-css-2.0.0.tgz", "integrity": "sha512-xGq5ISgcUP5cvGkS2MMFLtPDBtrtQPSFfC6gA6U8wHKqfjTIMZLZNxOItQnoSjdOzlXOLU/yD32RKC4SvjNbtA==", "license": "MIT"}, "node_modules/invariant": {"version": "2.2.4", "resolved": "https://registry.npmmirror.com/invariant/-/invariant-2.2.4.tgz", "integrity": "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-mobile": {"version": "2.2.2", "resolved": "https://registry.npmmirror.com/is-mobile/-/is-mobile-2.2.2.tgz", "integrity": "sha512-wW/SXnYJkTjs++tVK5b6kVITZpAZPtUrt9SF80vvxGiF/Oywal+COk1jlRkiVq15RFNEQKQY31TkV24/1T5cVg==", "license": "MIT"}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isomorphic-fetch": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz", "integrity": "sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==", "license": "MIT", "dependencies": {"node-fetch": "^1.0.1", "whatwg-fetch": ">=0.10.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "license": "MIT"}, "node_modules/json2mq": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/json2mq/-/json2mq-0.2.0.tgz", "integrity": "sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==", "license": "MIT", "dependencies": {"string-convert": "^0.2.0"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsplumb": {"version": "2.15.6", "resolved": "https://registry.npmmirror.com/jsplumb/-/jsplumb-2.15.6.tgz", "integrity": "sha512-sIpbpz5eMVM+vV+MQzFCidlaa1RsknrQs6LOTKYDjYUDdTAi2AN2bFi94TxB33TifcIsRNV1jebcaxg0tCoPzg==", "license": "(MIT OR GPL-2.0)"}, "node_modules/lightningcss": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.30.1.tgz", "integrity": "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==", "dev": true, "license": "MPL-2.0", "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.30.1", "lightningcss-darwin-x64": "1.30.1", "lightningcss-freebsd-x64": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1"}}, "node_modules/lightningcss-darwin-arm64": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz", "integrity": "sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-darwin-x64": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz", "integrity": "sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-freebsd-x64": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz", "integrity": "sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm-gnueabihf": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz", "integrity": "sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==", "cpu": ["arm"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm64-gnu": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz", "integrity": "sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm64-musl": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz", "integrity": "sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-x64-gnu": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz", "integrity": "sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-x64-musl": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz", "integrity": "sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-win32-arm64-msvc": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz", "integrity": "sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-win32-x64-msvc": {"version": "1.30.1", "resolved": "https://registry.npmmirror.com/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz", "integrity": "sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss/node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/lmdb": {"version": "2.8.5", "resolved": "https://registry.npmmirror.com/lmdb/-/lmdb-2.8.5.tgz", "integrity": "sha512-9bMdFfc80S+vSldBmG3HOuLVHnxRdNTlpzR6QDnzqCQtCzGUEAGTzBKYMeIM+I/sU4oZfgbcbS7X7F65/z/oxQ==", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"msgpackr": "^1.9.5", "node-addon-api": "^6.1.0", "node-gyp-build-optional-packages": "5.1.1", "ordered-binary": "^1.4.1", "weak-lru-cache": "^1.2.2"}, "bin": {"download-lmdb-prebuilds": "bin/download-prebuilds.js"}, "optionalDependencies": {"@lmdb/lmdb-darwin-arm64": "2.8.5", "@lmdb/lmdb-darwin-x64": "2.8.5", "@lmdb/lmdb-linux-arm": "2.8.5", "@lmdb/lmdb-linux-arm64": "2.8.5", "@lmdb/lmdb-linux-x64": "2.8.5", "@lmdb/lmdb-win32-x64": "2.8.5"}}, "node_modules/lmdb/node_modules/node-addon-api": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-6.1.0.tgz", "integrity": "sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==", "dev": true, "license": "MIT"}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==", "license": "MIT"}, "node_modules/lodash.throttle": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz", "integrity": "sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==", "license": "MIT"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mini-store": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/mini-store/-/mini-store-2.0.0.tgz", "integrity": "sha512-EG0CuwpQmX+XL4QVS0kxNwHW5ftSbhygu1qxQH0pipugjnPkbvkalCdQbEihMwtQY6d3MTN+MS0q+aurs+RfLQ==", "license": "MIT", "dependencies": {"hoist-non-react-statics": "^2.3.1", "prop-types": "^15.6.0", "react-lifecycles-compat": "^3.0.4", "shallowequal": "^1.0.2"}}, "node_modules/mini-store/node_modules/hoist-non-react-statics": {"version": "2.5.5", "resolved": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz", "integrity": "sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz", "integrity": "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==", "license": "MIT", "engines": {"node": "*"}}, "node_modules/mousetrap": {"version": "1.6.5", "resolved": "https://registry.npmmirror.com/mousetrap/-/mousetrap-1.6.5.tgz", "integrity": "sha512-QNo4kEepaIBwiT8CDhP98umTetp+JNfQYBWvC1pc6/OAibuXtRcxZ58Qz8skvEHYvURne/7R8T5VoOI7rDsEUA==", "license": "Apache-2.0 WITH LLVM-exception"}, "node_modules/msgpackr": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/msgpackr/-/msgpackr-1.11.5.tgz", "integrity": "sha512-UjkUHN0yqp9RWKy0Lplhh+wlpdt9oQBYgULZOiFhV3VclSF1JnSQWZ5r9gORQlNYaUKQoR8itv7g7z1xDDuACA==", "dev": true, "license": "MIT", "optionalDependencies": {"msgpackr-extract": "^3.0.2"}}, "node_modules/msgpackr-extract": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/msgpackr-extract/-/msgpackr-extract-3.0.3.tgz", "integrity": "sha512-P0efT1C9jIdVRefqjzOQ9Xml57zpOXnIuS+csaB4MdZbTdmGDLo8XhzBG1N7aO11gKDDkJvBLULeFTo46wwreA==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build-optional-packages": "5.2.2"}, "bin": {"download-msgpackr-prebuilds": "bin/download-prebuilds.js"}, "optionalDependencies": {"@msgpackr-extract/msgpackr-extract-darwin-arm64": "3.0.3", "@msgpackr-extract/msgpackr-extract-darwin-x64": "3.0.3", "@msgpackr-extract/msgpackr-extract-linux-arm": "3.0.3", "@msgpackr-extract/msgpackr-extract-linux-arm64": "3.0.3", "@msgpackr-extract/msgpackr-extract-linux-x64": "3.0.3", "@msgpackr-extract/msgpackr-extract-win32-x64": "3.0.3"}}, "node_modules/msgpackr-extract/node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": ">=8"}}, "node_modules/msgpackr-extract/node_modules/node-gyp-build-optional-packages": {"version": "5.2.2", "resolved": "https://registry.npmmirror.com/node-gyp-build-optional-packages/-/node-gyp-build-optional-packages-5.2.2.tgz", "integrity": "sha512-s+w+rBWnpTMwSFbaE0UXsRlg7hU4FjekKU4eyAih5T8nJuNZT1nNsskXpxmeqSK9UzkBl6UgRlnKc8hz8IEqOw==", "dev": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^2.0.1"}, "bin": {"node-gyp-build-optional-packages": "bin.js", "node-gyp-build-optional-packages-optional": "optional.js", "node-gyp-build-optional-packages-test": "build-test.js"}}, "node_modules/mutationobserver-shim": {"version": "0.3.7", "resolved": "https://registry.npmmirror.com/mutationobserver-shim/-/mutationobserver-shim-0.3.7.tgz", "integrity": "sha512-oRIDTyZQU96nAiz2AQyngwx1e89iApl2hN5AOYwyxLUB47UYsU3Wv9lJWqH5y/QdiYkc5HQLi23ZNB3fELdHcQ==", "license": "MIT"}, "node_modules/node-addon-api": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.1.1.tgz", "integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==", "dev": true, "license": "MIT"}, "node_modules/node-fetch": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.7.3.tgz", "integrity": "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==", "license": "MIT", "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}}, "node_modules/node-gyp-build-optional-packages": {"version": "5.1.1", "resolved": "https://registry.npmmirror.com/node-gyp-build-optional-packages/-/node-gyp-build-optional-packages-5.1.1.tgz", "integrity": "sha512-+P72GAjVAbTxjjwUmwjVrqrdZROD4nf8KgpBoDxqXXTiYZZt/ud60dE5yvCSr9lRO8e8yv6kgJIC0K0PfZFVQw==", "dev": true, "license": "MIT", "dependencies": {"detect-libc": "^2.0.1"}, "bin": {"node-gyp-build-optional-packages": "bin.js", "node-gyp-build-optional-packages-optional": "optional.js", "node-gyp-build-optional-packages-test": "build-test.js"}}, "node_modules/node-gyp-build-optional-packages/node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true, "license": "MIT"}, "node_modules/nullthrows": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/nullthrows/-/nullthrows-1.1.1.tgz", "integrity": "sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/omit.js": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/omit.js/-/omit.js-1.0.2.tgz", "integrity": "sha512-/QPc6G2NS+8d4L/cQhbk6Yit1WTB6Us2g84A7A/1+w9d/eRGHyEqC5kkQtHVoHZ5NFWGG7tUGgrhVZwgZanKrQ==", "license": "MIT", "dependencies": {"babel-runtime": "^6.23.0"}}, "node_modules/ordered-binary": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/ordered-binary/-/ordered-binary-1.6.0.tgz", "integrity": "sha512-IQh2aMfMIDbPjI/8a3Edr+PiOpcsB7yo8NdW7aHWVaoR/pcDldunMvnnwbk/auPGqmKeAdxtZl7MHX/QmPwhvQ==", "dev": true, "license": "MIT"}, "node_modules/parcel": {"version": "2.15.4", "resolved": "https://registry.npmmirror.com/parcel/-/parcel-2.15.4.tgz", "integrity": "sha512-eZHQ/omuQ7yBYB9XezyzSqhc826oy/uhloCNiej1CTZ+twAqJVtp4MRvTGMcivKhE+WE8QkYD5XkJHLLQsJQcg==", "dev": true, "license": "MIT", "dependencies": {"@parcel/config-default": "2.15.4", "@parcel/core": "2.15.4", "@parcel/diagnostic": "2.15.4", "@parcel/events": "2.15.4", "@parcel/feature-flags": "2.15.4", "@parcel/fs": "2.15.4", "@parcel/logger": "2.15.4", "@parcel/package-manager": "2.15.4", "@parcel/reporter-cli": "2.15.4", "@parcel/reporter-dev-server": "2.15.4", "@parcel/reporter-tracer": "2.15.4", "@parcel/utils": "2.15.4", "chalk": "^4.1.2", "commander": "^12.1.0", "get-port": "^4.2.0"}, "bin": {"parcel": "lib/bin.js"}, "engines": {"node": ">= 16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "dev": true, "license": "MIT"}, "node_modules/process": {"version": "0.11.10", "resolved": "https://registry.npmmirror.com/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://registry.npmmirror.com/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "license": "MIT", "dependencies": {"asap": "~2.0.3"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/raf": {"version": "3.4.1", "resolved": "https://registry.npmmirror.com/raf/-/raf-3.4.1.tgz", "integrity": "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==", "license": "MIT", "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/rc-align": {"version": "2.4.5", "resolved": "https://registry.npmmirror.com/rc-align/-/rc-align-2.4.5.tgz", "integrity": "sha512-nv9wYUYdfyfK+qskThf4BQUSIadeI/dCsfaMZfNEoxm9HwOIioQ+LyqmMK6jWHAZQgOzMLaqawhuBXlF63vgjw==", "license": "MIT", "dependencies": {"babel-runtime": "^6.26.0", "dom-align": "^1.7.0", "prop-types": "^15.5.8", "rc-util": "^4.0.4"}}, "node_modules/rc-animate": {"version": "2.11.1", "resolved": "https://registry.npmmirror.com/rc-animate/-/rc-animate-2.11.1.tgz", "integrity": "sha512-1NyuCGFJG/0Y+9RKh5y/i/AalUCA51opyyS/jO2seELpgymZm2u9QV3xwODwEuzkmeQ1BDPxMLmYLcTJedPlkQ==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.6", "css-animation": "^1.3.2", "prop-types": "15.x", "raf": "^3.4.0", "rc-util": "^4.15.3", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-calendar": {"version": "9.15.11", "resolved": "https://registry.npmmirror.com/rc-calendar/-/rc-calendar-9.15.11.tgz", "integrity": "sha512-qv0VXfAAnysMWJigxaP6se4bJHvr17D9qsLbi8BOpdgEocsS0RkgY1IUiFaOVYKJDy/EyLC447O02sV/y5YYBg==", "dependencies": {"babel-runtime": "6.x", "classnames": "2.x", "moment": "2.x", "prop-types": "^15.5.8", "rc-trigger": "^2.2.0", "rc-util": "^4.1.1", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-cascader": {"version": "0.17.5", "resolved": "https://registry.npmmirror.com/rc-cascader/-/rc-cascader-0.17.5.tgz", "integrity": "sha512-WYMVcxU0+Lj+xLr4YYH0+yXODumvNXDcVEs5i7L1mtpWwYkubPV/zbQpn+jGKFCIW/hOhjkU4J1db8/P/UKE7A==", "dependencies": {"array-tree-filter": "^2.1.0", "prop-types": "^15.5.8", "rc-trigger": "^2.2.0", "rc-util": "^4.0.4", "react-lifecycles-compat": "^3.0.4", "shallow-equal": "^1.0.0", "warning": "^4.0.1"}}, "node_modules/rc-checkbox": {"version": "2.1.8", "resolved": "https://registry.npmmirror.com/rc-checkbox/-/rc-checkbox-2.1.8.tgz", "integrity": "sha512-6qOgh0/by0nVNASx6LZnhRTy17Etcgav+IrI7kL9V9kcDZ/g7K14JFlqrtJ3NjDq/Kyn+BPI1st1XvbkhfaJeg==", "dependencies": {"babel-runtime": "^6.23.0", "classnames": "2.x", "prop-types": "15.x", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-collapse": {"version": "1.11.8", "resolved": "https://registry.npmmirror.com/rc-collapse/-/rc-collapse-1.11.8.tgz", "integrity": "sha512-8EhfPyScTYljkbRuIoHniSwZagD5UPpZ3CToYgoNYWC85L2qCbPYF7+OaC713FOrIkp6NbfNqXsITNxmDAmxog==", "license": "MIT", "dependencies": {"classnames": "2.x", "css-animation": "1.x", "prop-types": "^15.5.6", "rc-animate": "2.x", "react-is": "^16.7.0", "react-lifecycles-compat": "^3.0.4", "shallowequal": "^1.1.0"}}, "node_modules/rc-dialog": {"version": "7.6.1", "resolved": "https://registry.npmmirror.com/rc-dialog/-/rc-dialog-7.6.1.tgz", "integrity": "sha512-KUKf+2eZ4YL+lnXMG3hR4ZtIhC9glfH27NtTVz3gcoDIPAf3uUvaXVRNoDCiSi+OGKLyIb/b6EoidFh6nQC5Wg==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "rc-animate": "2.x", "rc-util": "^4.16.1"}}, "node_modules/rc-drawer": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/rc-drawer/-/rc-drawer-3.1.3.tgz", "integrity": "sha512-2z+RdxmzXyZde/1OhVMfDR1e/GBswFeWSZ7FS3Fdd0qhgVdpV1wSzILzzxRaT481ItB5hOV+e8pZT07vdJE8kg==", "dependencies": {"classnames": "^2.2.6", "rc-util": "^4.16.1", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": "*"}}, "node_modules/rc-dropdown": {"version": "2.4.1", "resolved": "https://registry.npmmirror.com/rc-dropdown/-/rc-dropdown-2.4.1.tgz", "integrity": "sha512-p0XYn0wrOpAZ2fUGE6YJ6U8JBNc5ASijznZ6dkojdaEfQJAeZtV9KMEewhxkVlxGSbbdXe10ptjBlTEW9vEwEg==", "license": "MIT", "dependencies": {"babel-runtime": "^6.26.0", "classnames": "^2.2.6", "prop-types": "^15.5.8", "rc-trigger": "^2.5.1", "react-lifecycles-compat": "^3.0.2"}}, "node_modules/rc-editor-core": {"version": "0.8.10", "resolved": "https://registry.npmmirror.com/rc-editor-core/-/rc-editor-core-0.8.10.tgz", "integrity": "sha512-T3aHpeMCIYA1sdAI7ynHHjXy5fqp83uPlD68ovZ0oClTSc3tbHmyCxXlA+Ti4YgmcpCYv7avF6a+TIbAka53kw==", "dependencies": {"babel-runtime": "^6.26.0", "classnames": "^2.2.5", "draft-js": "^0.10.0", "immutable": "^3.7.4", "lodash": "^4.16.5", "prop-types": "^15.5.8", "setimmediate": "^1.0.5"}, "peerDependencies": {"react": ">=15.0.0", "react-dom": ">=15.0.0"}}, "node_modules/rc-editor-core/node_modules/draft-js": {"version": "0.10.5", "resolved": "https://registry.npmmirror.com/draft-js/-/draft-js-0.10.5.tgz", "integrity": "sha512-LE6jSCV9nkPhfVX2ggcRLA4FKs6zWq9ceuO/88BpXdNCS7mjRTgs0NsV6piUCJX9YxMsB9An33wnkMmU2sD2Zg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"fbjs": "^0.8.15", "immutable": "~3.7.4", "object-assign": "^4.1.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0", "react-dom": "^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0"}}, "node_modules/rc-editor-mention": {"version": "1.1.13", "resolved": "https://registry.npmmirror.com/rc-editor-mention/-/rc-editor-mention-1.1.13.tgz", "integrity": "sha512-3AOmGir91Fi2ogfRRaXLtqlNuIwQpvla7oUnGHS1+3eo7b+fUp5IlKcagqtwUBB5oDNofoySXkLBxzWvSYNp/Q==", "dependencies": {"babel-runtime": "^6.23.0", "classnames": "^2.2.5", "dom-scroll-into-view": "^1.2.0", "draft-js": "~0.10.0", "immutable": "~3.7.4", "prop-types": "^15.5.8", "rc-animate": "^2.3.0", "rc-editor-core": "~0.8.3"}, "peerDependencies": {"react": ">=15.x", "react-dom": ">=15.x"}}, "node_modules/rc-editor-mention/node_modules/draft-js": {"version": "0.10.5", "resolved": "https://registry.npmmirror.com/draft-js/-/draft-js-0.10.5.tgz", "integrity": "sha512-LE6jSCV9nkPhfVX2ggcRLA4FKs6zWq9ceuO/88BpXdNCS7mjRTgs0NsV6piUCJX9YxMsB9An33wnkMmU2sD2Zg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"fbjs": "^0.8.15", "immutable": "~3.7.4", "object-assign": "^4.1.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0", "react-dom": "^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0"}}, "node_modules/rc-form": {"version": "2.4.12", "resolved": "https://registry.npmmirror.com/rc-form/-/rc-form-2.4.12.tgz", "integrity": "sha512-sHfyWRrnjCHkeCYfYAGop2GQBUC6CKMPcJF9h/gL/vTmZB/RN6fNOGKjXrXjFbwFwKXUWBoPtIDDDmXQW9xNdw==", "license": "MIT", "dependencies": {"async-validator": "~1.11.3", "babel-runtime": "6.x", "create-react-class": "^15.5.3", "dom-scroll-into-view": "1.x", "hoist-non-react-statics": "^3.3.0", "lodash": "^4.17.4", "rc-util": "^4.15.3", "react-is": "^16.13.1", "warning": "^4.0.3"}, "peerDependencies": {"prop-types": "^15.0"}}, "node_modules/rc-hammerjs": {"version": "0.6.10", "resolved": "https://registry.npmmirror.com/rc-hammerjs/-/rc-hammerjs-0.6.10.tgz", "integrity": "sha512-Vgh9qIudyN5CHRop4M+v+xUniQBFWXKrsJxQRVtJOi2xgRrCeI52/bkpaL5HWwUhqTK9Ayq0n7lYTItT6ld5rg==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "hammerjs": "^2.0.8", "prop-types": "^15.5.9"}}, "node_modules/rc-input-number": {"version": "4.5.9", "resolved": "https://registry.npmmirror.com/rc-input-number/-/rc-input-number-4.5.9.tgz", "integrity": "sha512-wAT4EBpLDW4+27c935k4F1JLk+gnhyGBkpzBmtkNvIHLG8yTndZSJ2bFfSYfkA6C82IxmAztXs3ffCeUd/rkbg==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.0", "prop-types": "^15.5.7", "rc-util": "^4.5.1", "rmc-feedback": "^2.0.0"}}, "node_modules/rc-mentions": {"version": "0.4.2", "resolved": "https://registry.npmmirror.com/rc-mentions/-/rc-mentions-0.4.2.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>urQzacLXOfVuiHydGzqkq7cFMHXF18l2jZ9PhWUn2cqvOSY3W4osN0Pq29AOMOBpcxdZCzgc7Lb0r/bgkDw==", "license": "MIT", "dependencies": {"@ant-design/create-react-context": "^0.2.4", "classnames": "^2.2.6", "rc-menu": "^7.4.22", "rc-trigger": "^2.6.2", "rc-util": "^4.6.0", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": "*"}}, "node_modules/rc-menu": {"version": "7.5.5", "resolved": "https://registry.npmmirror.com/rc-menu/-/rc-menu-7.5.5.tgz", "integrity": "sha512-4YJXJgrpUGEA1rMftXN7bDhrV5rPB8oBJoHqT+GVXtIWCanfQxEnM3fmhHQhatL59JoAFMZhJaNzhJIk4FUWCQ==", "license": "MIT", "dependencies": {"classnames": "2.x", "dom-scroll-into-view": "1.x", "mini-store": "^2.0.0", "mutationobserver-shim": "^0.3.2", "rc-animate": "^2.10.1", "rc-trigger": "^2.3.0", "rc-util": "^4.13.0", "resize-observer-polyfill": "^1.5.0", "shallowequal": "^1.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-notification": {"version": "3.3.1", "resolved": "https://registry.npmmirror.com/rc-notification/-/rc-notification-3.3.1.tgz", "integrity": "sha512-U5+f4BmBVfMSf3OHSLyRagsJ74yKwlrQAtbbL5ijoA0F2C60BufwnOcHG18tVprd7iaIjzZt1TKMmQSYSvgrig==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "classnames": "2.x", "prop-types": "^15.5.8", "rc-animate": "2.x", "rc-util": "^4.0.4"}}, "node_modules/rc-pagination": {"version": "1.20.15", "resolved": "https://registry.npmmirror.com/rc-pagination/-/rc-pagination-1.20.15.tgz", "integrity": "sha512-/Xr4/3GOa1DtL8iCYl7qRUroEMrRDhZiiuHwcVFfSiwa9LYloMlUWcOJsnr8LN6A7rLPdm3/CHStUNeYd+2pKw==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.6", "prop-types": "^15.5.7", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-progress": {"version": "2.5.3", "resolved": "https://registry.npmmirror.com/rc-progress/-/rc-progress-2.5.3.tgz", "integrity": "sha512-K2fa4CnqGehLZoMrdmBeZ86ONSTVcdk5FlqetbwJ3R/+42XfqhwQVOjWp2MH4P7XSQOMAGcNOy1SFfCP3415sg==", "dependencies": {"babel-runtime": "6.x", "prop-types": "^15.5.8"}}, "node_modules/rc-rate": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/rc-rate/-/rc-rate-2.5.1.tgz", "integrity": "sha512-3iJkNJT8xlHklPCdeZtUZmJmRVUbr6AHRlfSsztfYTXVlHrv2TcPn3XkHsH+12j812WVB7gvilS2j3+ffjUHXg==", "dependencies": {"classnames": "^2.2.5", "prop-types": "^15.5.8", "rc-util": "^4.3.0", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-select": {"version": "9.2.3", "resolved": "https://registry.npmmirror.com/rc-select/-/rc-select-9.2.3.tgz", "integrity": "sha512-WhswxOMWiNnkXRbxyrj0kiIvyCfo/BaRPaYbsDetSIAU2yEDwKHF798blCP5u86KLOBKBvtxWLFCkSsQw1so5w==", "license": "MIT", "dependencies": {"babel-runtime": "^6.23.0", "classnames": "2.x", "component-classes": "1.x", "dom-scroll-into-view": "1.x", "prop-types": "^15.5.8", "raf": "^3.4.0", "rc-animate": "2.x", "rc-menu": "^7.3.0", "rc-trigger": "^2.5.4", "rc-util": "^4.0.4", "react-lifecycles-compat": "^3.0.2", "warning": "^4.0.2"}}, "node_modules/rc-slider": {"version": "8.7.1", "resolved": "https://registry.npmmirror.com/rc-slider/-/rc-slider-8.7.1.tgz", "integrity": "sha512-WMT5mRFUEcrLWwTxsyS8jYmlaMsTVCZIGENLikHsNv+tE8ThU2lCoPfi/xFNUfJFNFSBFP3MwPez9ZsJmNp13g==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.5", "prop-types": "^15.5.4", "rc-tooltip": "^3.7.0", "rc-util": "^4.0.4", "react-lifecycles-compat": "^3.0.4", "shallowequal": "^1.1.0", "warning": "^4.0.3"}}, "node_modules/rc-steps": {"version": "3.5.0", "resolved": "https://registry.npmmirror.com/rc-steps/-/rc-steps-3.5.0.tgz", "integrity": "sha512-2Vkkrpa7PZbg7qPsqTNzVDov4u78cmxofjjnIHiGB9+9rqKS8oTLPzbW2uiWDr3Lk+yGwh8rbpGO1E6VAgBCOg==", "dependencies": {"babel-runtime": "^6.23.0", "classnames": "^2.2.3", "lodash": "^4.17.5", "prop-types": "^15.5.7"}}, "node_modules/rc-tabs": {"version": "9.7.0", "resolved": "https://registry.npmmirror.com/rc-tabs/-/rc-tabs-9.7.0.tgz", "integrity": "sha512-kvmgp8/MfLzFZ06hWHignqomFQ5nF7BqKr5O1FfhE4VKsGrep52YSF/1MvS5oe0NPcI9XGNS2p751C5v6cYDpQ==", "license": "MIT", "dependencies": {"@ant-design/create-react-context": "^0.2.4", "babel-runtime": "6.x", "classnames": "2.x", "lodash": "^4.17.5", "prop-types": "15.x", "raf": "^3.4.1", "rc-hammerjs": "~0.6.0", "rc-util": "^4.0.4", "react-lifecycles-compat": "^3.0.4", "resize-observer-polyfill": "^1.5.1", "warning": "^4.0.3"}, "peerDependencies": {"react": ">=15.0.0"}}, "node_modules/rc-time-picker": {"version": "3.7.3", "resolved": "https://registry.npmmirror.com/rc-time-picker/-/rc-time-picker-3.7.3.tgz", "integrity": "sha512-Lv1Mvzp9fRXhXEnRLO4nW6GLNxUkfAZ3RsiIBsWjGjXXvMNjdr4BX/ayElHAFK0DoJqOhm7c5tjmIYpEOwcUXg==", "dependencies": {"classnames": "2.x", "moment": "2.x", "prop-types": "^15.5.8", "raf": "^3.4.1", "rc-trigger": "^2.2.0", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-tooltip": {"version": "3.7.3", "resolved": "https://registry.npmmirror.com/rc-tooltip/-/rc-tooltip-3.7.3.tgz", "integrity": "sha512-dE2ibukxxkrde7wH9W8ozHKUO4aQnPZ6qBHtrTH9LoO836PjDdiaWO73fgPB05VfJs9FbZdmGPVEbXCeOP99Ww==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "prop-types": "^15.5.8", "rc-trigger": "^2.2.2"}}, "node_modules/rc-tree": {"version": "2.1.4", "resolved": "https://registry.npmmirror.com/rc-tree/-/rc-tree-2.1.4.tgz", "integrity": "sha512-Xey794Iavgs8YldFlXcZLOhfcIhlX5Oz/yfKufknBXf2AlZCOkc7aHqSM9uTF7fBPtTGPhPxNEfOqHfY7b7xng==", "license": "MIT", "dependencies": {"@ant-design/create-react-context": "^0.2.4", "classnames": "2.x", "prop-types": "^15.5.8", "rc-animate": "^2.6.0", "rc-util": "^4.5.1", "react-lifecycles-compat": "^3.0.4", "warning": "^4.0.3"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-tree-select": {"version": "2.9.4", "resolved": "https://registry.npmmirror.com/rc-tree-select/-/rc-tree-select-2.9.4.tgz", "integrity": "sha512-0HQkXAN4XbfBW20CZYh3G+V+VMrjX42XRtDCpyv6PDUm5vikC0Ob682ZBCVS97Ww2a5Hf6Ajmu0ahWEdIEpwhg==", "license": "MIT", "dependencies": {"classnames": "^2.2.1", "dom-scroll-into-view": "^1.2.1", "prop-types": "^15.5.8", "raf": "^3.4.0", "rc-animate": "^2.8.2", "rc-tree": "~2.1.0", "rc-trigger": "^3.0.0", "rc-util": "^4.5.0", "react-lifecycles-compat": "^3.0.4", "shallowequal": "^1.0.2", "warning": "^4.0.1"}}, "node_modules/rc-tree-select/node_modules/rc-trigger": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-3.0.0.tgz", "integrity": "sha512-hQxbbJpo23E2QnYczfq3Ec5J5tVl2mUDhkqxrEsQAqk16HfADQg+iKNWzEYXyERSncdxfnzYuaBgy764mNRzTA==", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.6", "prop-types": "15.x", "raf": "^3.4.0", "rc-align": "^2.4.1", "rc-animate": "^3.0.0-rc.1", "rc-util": "^4.15.7"}}, "node_modules/rc-tree-select/node_modules/rc-trigger/node_modules/rc-animate": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/rc-animate/-/rc-animate-3.1.1.tgz", "integrity": "sha512-8wg2Zg3EETy0k/9kYuis30NJNQg1D6/WSQwnCiz6SvyxQXNet/rVraRz3bPngwY6rcU2nlRvoShiYOorXyF7Sg==", "license": "MIT", "dependencies": {"@ant-design/css-animation": "^1.7.2", "classnames": "^2.2.6", "raf": "^3.4.0", "rc-util": "^4.15.3"}}, "node_modules/rc-trigger": {"version": "2.6.5", "resolved": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-2.6.5.tgz", "integrity": "sha512-m6Cts9hLeZWsTvWnuMm7oElhf+03GOjOLfTuU0QmdB9ZrW7jR2IpI5rpNM7i9MvAAlMAmTx5Zr7g3uu/aMvZAw==", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.6", "prop-types": "15.x", "rc-align": "^2.4.0", "rc-animate": "2.x", "rc-util": "^4.4.0", "react-lifecycles-compat": "^3.0.4"}}, "node_modules/rc-upload": {"version": "2.9.4", "resolved": "https://registry.npmmirror.com/rc-upload/-/rc-upload-2.9.4.tgz", "integrity": "sha512-WXt0HGxXyzLrPV6iec/96Rbl/6dyrAW8pKuY6wwD7yFYwfU5bjgKjv7vC8KNMJ6wzitFrZjnoiogNL3dF9dj3Q==", "license": "MIT", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.5", "prop-types": "^15.5.7", "warning": "4.x"}}, "node_modules/rc-util": {"version": "4.21.1", "resolved": "https://registry.npmmirror.com/rc-util/-/rc-util-4.21.1.tgz", "integrity": "sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg==", "license": "MIT", "dependencies": {"add-dom-event-listener": "^1.1.0", "prop-types": "^15.5.10", "react-is": "^16.12.0", "react-lifecycles-compat": "^3.0.4", "shallowequal": "^1.1.0"}}, "node_modules/react": {"version": "17.0.2", "resolved": "https://registry.npmmirror.com/react/-/react-17.0.2.tgz", "integrity": "sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dnd": {"version": "16.0.1", "resolved": "https://registry.npmmirror.com/react-dnd/-/react-dnd-16.0.1.tgz", "integrity": "sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==", "license": "MIT", "dependencies": {"@react-dnd/invariant": "^4.0.1", "@react-dnd/shallowequal": "^4.0.1", "dnd-core": "^16.0.1", "fast-deep-equal": "^3.1.3", "hoist-non-react-statics": "^3.3.2"}, "peerDependencies": {"@types/hoist-non-react-statics": ">= 3.3.1", "@types/node": ">= 12", "@types/react": ">= 16", "react": ">= 16.14"}, "peerDependenciesMeta": {"@types/hoist-non-react-statics": {"optional": true}, "@types/node": {"optional": true}, "@types/react": {"optional": true}}}, "node_modules/react-dnd-html5-backend": {"version": "9.5.1", "resolved": "https://registry.npmmirror.com/react-dnd-html5-backend/-/react-dnd-html5-backend-9.5.1.tgz", "integrity": "sha512-wUdzjREwLqHxFkA6E+XDVL5IFjRDbBI3SHVKil9n3qrGT5dm2tA2oi1aIALdfMKsu00c+OXA9lz/LuKZCE9KXg==", "license": "MIT", "dependencies": {"dnd-core": "^9.5.1"}}, "node_modules/react-dnd-html5-backend/node_modules/dnd-core": {"version": "9.5.1", "resolved": "https://registry.npmmirror.com/dnd-core/-/dnd-core-9.5.1.tgz", "integrity": "sha512-/yEWFF2jg51yyB8uA2UbvBr9Qis0Oo/4p9cqHLEKZdxzHHVSPfq0a/ool8NG6dIS6Q4uN+oKGObY0rNWiopJDA==", "license": "MIT", "dependencies": {"@types/asap": "^2.0.0", "@types/invariant": "^2.2.30", "asap": "^2.0.6", "invariant": "^2.2.4", "redux": "^4.0.4"}}, "node_modules/react-dom": {"version": "17.0.2", "resolved": "https://registry.npmmirror.com/react-dom/-/react-dom-17.0.2.tgz", "integrity": "sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1", "scheduler": "^0.20.2"}, "peerDependencies": {"react": "17.0.2"}}, "node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "license": "MIT"}, "node_modules/react-lifecycles-compat": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz", "integrity": "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==", "license": "MIT"}, "node_modules/react-refresh": {"version": "0.16.0", "resolved": "https://registry.npmmirror.com/react-refresh/-/react-refresh-0.16.0.tgz", "integrity": "sha512-FPvF2XxTSikpJxcr+bHut2H4gJ17+18Uy20D5/F+SKzFap62R3cM5wH6b8WN3LyGSYeQilLEcJcR1fjBSI2S1A==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/redux": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/redux/-/redux-4.2.1.tgz", "integrity": "sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.9.2"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==", "dev": true, "license": "MIT"}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==", "license": "MIT"}, "node_modules/rmc-feedback": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/rmc-feedback/-/rmc-feedback-2.0.0.tgz", "integrity": "sha512-5PWOGOW7VXks/l3JzlOU9NIxRpuaSS8d9zA3UULUCuTKnpwBHNvv1jSJzxgbbCQeYzROWUpgKI4za3X4C/mKmQ==", "dependencies": {"babel-runtime": "6.x", "classnames": "^2.2.5"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/scheduler": {"version": "0.20.2", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.20.2.tgz", "integrity": "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==", "license": "MIT"}, "node_modules/shallow-equal": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/shallow-equal/-/shallow-equal-1.2.1.tgz", "integrity": "sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA==", "license": "MIT"}, "node_modules/shallowequal": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/shallowequal/-/shallowequal-1.1.0.tgz", "integrity": "sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==", "license": "MIT"}, "node_modules/string-convert": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/string-convert/-/string-convert-0.2.1.tgz", "integrity": "sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==", "license": "MIT"}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/term-size": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/term-size/-/term-size-2.2.1.tgz", "integrity": "sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/tinycolor2": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/tinycolor2/-/tinycolor2-1.6.0.tgz", "integrity": "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==", "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toggle-selection": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/toggle-selection/-/toggle-selection-1.0.6.tgz", "integrity": "sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==", "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ua-parser-js": {"version": "0.7.40", "resolved": "https://registry.npmmirror.com/ua-parser-js/-/ua-parser-js-0.7.40.tgz", "integrity": "sha512-us1E3K+3jJppDBa3Tl0L3MOJiGhe1C6P0+nIvQAFYbxlMAx0h81eOwLmU57xgqToduDDPx3y5QsdjPfDu+FgOQ==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/utility-types": {"version": "3.11.0", "resolved": "https://registry.npmmirror.com/utility-types/-/utility-types-3.11.0.tgz", "integrity": "sha512-6Z7Ma2aVEWisaL6TvBCy7P8rm2LQoPv6dJ7ecIaIixHcwfbJ0x7mWdbcwlIM5IGQxPZSFYeqRCqlOOeKoJYMkw==", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/warning": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/warning/-/warning-4.0.3.tgz", "integrity": "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/weak-lru-cache": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/weak-lru-cache/-/weak-lru-cache-1.2.2.tgz", "integrity": "sha512-DEAoo25RfSYMuTGc9vPJzZcZullwIqRDSI9LOy+fkCJPi6hykCnfKaXTuPBDuXAUcqHXyOgFtHNp/kB2FjYHbw==", "dev": true, "license": "MIT"}, "node_modules/whatwg-fetch": {"version": "3.6.20", "resolved": "https://registry.npmmirror.com/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz", "integrity": "sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==", "license": "MIT"}}}