import "./App.css";
import "./base.css";

import React, {useEffect, useMemo, useState, useRef, Fragment} from "react";
import {Icon, InputNumber, Menu, Modal, Tabs} from "antd";
import {jsPlumb} from "jsplumb";
import HTML5Backend from "react-dnd-html5-backend";
import {DndProvider, useDrag, useDrop} from "react-dnd";
import {cloneDeep} from "lodash";

export default function App(body) {
    const ui_window = [useRef(null), useRef(null)];
    const baseJsPlumb = useMemo(() => jsPlumb.getInstance(), []);
    const getDropProps = (refKey) => () => ({
        accept: ["title"],
        collect: (monitor) => ({isOver: !!monitor.isOver()}),
        drop: ({title}, monitor) => {
            const isFrame = title === "当过了N秒时";
            const getLeftTop = (rect, {x, y}) => {
                if (isFrame) return {left: 20, top: 20, updateHeight: 80};
                else {
                    let left = 0;
                    let top = 0;
                    let updateHeight = 0;
                    if (x - rect.x - 108 > 0) {
                        if (x + 108 < rect.x + rect.width) left = x - rect.x - 108;
                        else {
                            console.log({rect, x});
                            left = rect.width - 216;
                        }
                    }
                    if (y - rect.y - 18 > 0) {
                        if (y + 18 < rect.y + rect.height) top = y - rect.y - 18;
                        else {
                            top = y - rect.y;
                            updateHeight = 40;
                        }
                    }
                    return {left, top, updateHeight};
                }
            };
            setChartData((preChartData) => {
                let newChartData = cloneDeep(preChartData);

                const ui_parent = ui_window[refKey - 1].current;
                const {left, top, updateHeight = 0}
                    = getLeftTop(ui_parent.getBoundingClientRect(), monitor.getClientOffset());
                const newNode = {
                    id: Date.now(), title, type: isFrame ? "frame" : "node",
                    style: {left, top, height: isFrame ? 80 : "max-content"}, subNodeList: []
                };
                newChartData[refKey - 1].style.height += updateHeight;
                newChartData[refKey - 1].nodeList.push(newNode);
                return newChartData;
            });
        },
    });

    const [dropInfo1, ref_drop1] = useDrop(getDropProps(1));
    const [dropInfo2, ref_drop2] = useDrop(getDropProps(2));
    const refDrops = [ref_drop1, ref_drop2];
    const baseChartData = [
        {
            title: "初始化执行的方法 (只执行一次)",
            nodeList: [],
            style: {width: 280, height: 80, left: 20, top: 0},
        },
        {
            title: "主循环执行的方法 (每秒执行60次)",
            nodeList: [],
            style: {width: 280, height: 80, left: 360, top: 0},
        },
    ];
    const [chartData, setChartData] = useState(baseChartData);
    const baseInfoList = [
        {title: "运动动力求解", type: "title", key: "p1", open: true},
        {
            type: "sub",
            parent: "p1",
            list: [
                {
                    title: "IK解算手关节位置",
                    desc: "给定左右手需要移动到的目标位姿，通过逆运动学计算得到左右手臂每个关节的位姿，并将数据存储到一个向量中",
                },
                {
                    title: "IK解算脚关节位置",
                    desc: "给定左右脚需要移动到的目标位姿，通过逆运动学计算得到左右腿每个关节的位姿，并将数据存储到一个向量中",
                },
            ],
        },
        {title: "电机控制", type: "title", key: "p2", open: true},
        {
            type: "sub",
            parent: "p2",
            list: [
                {
                    title: "设置期望电机数据",
                    desc: "根据IK解算后的数据或者wbc计算的数据，算出所有电机的期望位置、速度、扭矩",
                },
                {
                    title: "计算电机扭矩",
                    desc: "根据算出所有电机的期望位置、速度、扭矩，计算出所有电机实际要输出的扭矩，可以单独设置每个关节的PD增益",
                },
            ],
        },
        {title: "Mujoco接口", type: "title", key: "p3", open: true},
        {
            type: "sub",
            parent: "p3",
            list: [
                {
                    title: "更新传感器数值",
                    desc: "更新mujoco仿真环境中所有传感器的数据，一般这个代码块要放到主循环最开始位置",
                },
                {
                    title: "设置关节电机扭矩",
                    desc: "驱动mujoco中所有电机执行器，让机器人的关节动起来，一般这个代码块要在主循环最后的位置",
                },
            ],
        },
        {title: "条件判断", type: "title", key: "p4", open: true},
        {
            type: "sub",
            parent: "p4",
            list: [{title: "当过了N秒时", desc: "设置仿真开始后过了N秒后的一个条件判断，需要放到主循环内"}],
        },
    ];
    const [menuInfoList, setMenuInfoList] = useState(baseInfoList);
    const ref_node = useRef({});
    const ref_HasInit = useRef([]);
    const [valueMap, setValueMap] = useState({});
    useEffect(() => {
        baseJsPlumb.ready(() => {
            baseJsPlumb.draggable(ui_window[0].current);
            baseJsPlumb.draggable(ui_window[1].current);
            baseJsPlumb.bind("beforeDrop", function (info) {
                const {sourceId, targetId} = info;
                return sourceId !== targetId;
            });
            baseJsPlumb.bind("connection", function (info, event) {
                console.log(info)
                console.log(event);
            });
        });
    }, []);

    // 当匀速新增时, 根据实际情况初始化元素的jsplumb属性
    useEffect(() => {
        const common = {
            isSource: true, isTarget: true, endpoint: "Dot",
            paintStyle: {fill: '#fff', radius: 4}, hoverPaintStyle: {radius: 8},
            connector: ['Flowchart', {stub: [12, 12], gap: 1, cornerRadius: 1, alwaysRespectStubs: true}],
            connectorStyle: {outlineStroke: "black", strokeWidth: 1},
            connectorOverlays: [['Arrow', {length: 12, location: 1}]],
        };
        for (const key in ref_node.current) {
            // 已经初始化过的节点不再新增
            if (ref_HasInit.current.includes(key)) continue;
            const {el, parent} = ref_node.current[key];
            baseJsPlumb.draggable(el, {containment: parent});
            baseJsPlumb.addEndpoint(el, {anchor: "Top", id: `${key}-top`}, common);
            baseJsPlumb.addEndpoint(el, {anchor: "Bottom", id: `${key}-down`}, common);
            ref_HasInit.current.push(key);
        }
    }, [chartData]);

    // 渲染每个框架里的子元素
    const renderSubNode = (nodeList, parent, parentIndexList) => {
        if (!nodeList.length) return <Fragment></Fragment>;
        return (
            <Fragment>
                {nodeList.map((node, index) => {
                    const {id, title, type, style, subNodeList = []} = node;
                    const refFunc = (el) => (ref_node.current[id] = {el, parent});

                    const showSetting = !["更新传感器数值", "设置关节电机扭矩", "当过了N秒时"].includes(title);
                    switch (type) {
                        case "node": {
                            return (
                                <div key={id} id={id} ref={refFunc} className={type} style={style}>
                                    <span>{title}</span>
                                    <div>{showSetting && <Icon type="setting"/>}</div>
                                    <div>
                                        <Icon type="delete"/>
                                    </div>
                                </div>
                            );
                        }
                        case "frame": {
                            const [info, ref_drop] = useDrop(() => ({
                                accept: ["title"], drop: ({title}) => {
                                    // 先不允许嵌套框架
                                    if (title === "当过了N秒时") return false;
                                    setChartData(chartData => {
                                        let newChartData = cloneDeep(chartData);
                                        const newNode = {id: Date.now(), title, type: "node", style: {left: 0, top: 0}};

                                        const index_frame = newChartData[parentIndexList[0]].nodeList.findIndex(info => info.id === id);
                                        newChartData[parentIndexList[0]].nodeList[index_frame].subNodeList.push(newNode);
                                        return newChartData;
                                    });
                                },
                            }));
                            if (!valueMap[id]) setValueMap(pre => ({...pre, [id]: 1}));
                            const onChange = ({target: {value}}) =>
                                setValueMap(pre => ({...pre, [id]: Math.min(999, value)}));
                            return (
                                <fieldset ref={refFunc} key={id} className={type} style={style}>
                                    <legend>
                                        <span>当过了</span>
                                        <input value={valueMap[id] || 1} onChange={onChange} type={"number"} min={0}
                                               step={1} style={{width: `${valueMap[id]}`.length * 8}}/>
                                        <span>秒时</span>
                                    </legend>
                                    <div ref={ref_drop} className={"drop"}>
                                        {renderSubNode(subNodeList, ref_node.current[id], parentIndexList.concat(index))}
                                    </div>
                                </fieldset>
                            );
                        }
                    }
                })}
            </Fragment>
        );
    };

    return (
        <div className="process-building">
            <div className={"dir"}>
                {menuInfoList.map(({title, type, key, open, parent, list}, index) => {
                    switch (type) {
                        case "title": {
                            const onClick = () => {
                                setMenuInfoList(
                                    menuInfoList.map((v) => {
                                        if (v.key === key) return {...v, open: !open};
                                        return v;
                                    })
                                );
                            };
                            return (
                                <div key={index} className={"title"} onClick={onClick}>
                                    <Icon className={"arrow-container"} style={{rotate: open ? "0deg" : "90deg"}}
                                          type="caret-right"/>
                                    <span>{title}</span>
                                </div>
                            );
                        }
                        case "sub": {
                            const isOpen = menuInfoList.find(({key}) => parent === key).open;
                            return (
                                <ul key={index} style={{maxHeight: isOpen ? list.length * 32 : 0}}>
                                    {list.map(({title, desc}, i) => {
                                        const [{isDragging}, drag] = useDrag(
                                            () => ({
                                                item: {title},
                                                type: "title",
                                                collect: (monitor) => ({isDragging: !!monitor.isDragging()}),
                                            }),
                                            [title, i]
                                        );
                                        return (
                                            <li ref={drag} key={`${parent}-${i}`} title={desc}
                                                style={{opacity: isDragging ? 0.5 : 1}}>
                                                {title}
                                            </li>
                                        );
                                    })}
                                </ul>
                            );
                        }
                    }
                })}
            </div>
            <div className="container">
                <div id="flow-main">
                    {chartData.map((chartInfo, index) => {
                        const {title, style, nodeList} = chartInfo;

                        // 下面都是长宽调整
                        const [resizing, setResizing] = useState(false);
                        const [resizeStart, setResizeStart] = useState({x: 0, y: 0, width: 0, height: 0});
                        const onMouseDown = (e) => {
                            e.stopPropagation();
                            setResizing(true);
                            setResizeStart({x: e.clientX, y: e.clientY, width: style.width, height: style.height});
                            document.body.style.cursor = "nwse-resize";
                        };
                        useEffect(() => {
                            if (!resizing) return;
                            const minWidth = nodeList.reduce((pre, {style: {left = 0}}) => Math.max(pre, left + 216), 280);
                            const minHeight = nodeList.reduce((pre, {style: {top = 0}}) => Math.max(pre, top + 36), 80);
                            const onMouseMove = (e) => {
                                const dx = e.clientX - resizeStart.x;
                                const dy = e.clientY - resizeStart.y;
                                setChartData((prev) => {
                                    const next = cloneDeep(prev);
                                    next[index].style.width = Math.max(minWidth, resizeStart.width + dx);
                                    next[index].style.height = Math.max(minHeight, resizeStart.height + dy);
                                    return next;
                                });
                            };
                            const onMouseUp = () => {
                                setResizing(false);
                                document.body.style.cursor = "";
                            };
                            window.addEventListener("mousemove", onMouseMove);
                            window.addEventListener("mouseup", onMouseUp);
                            return () => {
                                window.removeEventListener("mousemove", onMouseMove);
                                window.removeEventListener("mouseup", onMouseUp);
                            };
                        }, [resizing, resizeStart]);
                        //  上面都是长宽调整

                        return (
                            <div key={title} ref={ui_window[index]} className={"window"} style={style}>
                                <span className={"main-title"}>{title}</span>
                                <div ref={refDrops[index]} className={"drop"}>
                                    {renderSubNode(nodeList, ui_window[index].current, [index])}
                                </div>
                                <div className={"resizing"} onMouseDownCapture={onMouseDown} title="拉伸窗口">
                                    <Icon type="arrows-alt"/>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}
